# Fonctionnalités de l'Application Assurance iA Montréal

## 🎯 Vue d'ensemble

Cette application Laravel 12 offre une solution complète pour un assureur privé avec toutes les fonctionnalités modernes attendues dans le secteur de l'assurance.

## 🏠 Site Public

### Page d'Accueil (`/`)
- **Design professionnel** avec gradient bleu et blanc
- **Hero section** avec call-to-action vers simulation et RDV
- **Présentation des services** avec icônes et descriptions
- **Section "Pourquoi nous choisir"** avec avantages
- **Responsive design** optimisé mobile et desktop

### Page Services (`/services`)
- **Grille de services** détaillée avec 4 produits principaux
- **Informations spécifiques** par type d'assurance
- **Process en 4 étapes** expliqué visuellement
- **FAQ section** avec questions fréquentes
- **Call-to-action** vers simulation et RDV

### Simulateur d'Assurance (`/simulation`)
- **Interface intuitive** avec sélection par radio buttons
- **Calcul dynamique** selon le type d'assurance choisi
- **Validation côté client et serveur**
- **JavaScript interactif** pour adapter les champs
- **Progression en 3 étapes** : Simulation → Coordonnées → Résultat

## 💼 Fonctionnalités Business

### Calcul de Primes Intelligent
```php
// Visa Visiteur
$baseRate = 2.5; // $ par jour par 1000$ de couverture
$ageMultiplier = $age > 65 ? 2.5 : ($age > 50 ? 1.8 : 1);
$prime = ($coverage / 1000) * $baseRate * $duration * $ageMultiplier;

// Assurance Vie
$baseRate = 0.8; // $ par mois par 1000$ de couverture
$ageMultiplier = 1 + (($age - 25) * 0.02);
$prime = ($coverage / 1000) * $baseRate * 12 * $ageMultiplier;
```

### Génération de Leads
- **Capture automatique** des informations client
- **Stockage JSON** des données de simulation
- **Statuts de qualification** : new, contacted, converted, lost
- **Calcul automatique** du montant estimé

### Système de Rendez-vous
- **Créneaux automatisés** : Lun-Ven 9h-17h (sauf 12h-13h)
- **Vérification de disponibilité** en temps réel
- **Confirmation par email** automatique
- **Interface admin** pour validation

## 📧 Système de Notifications

### Emails Automatisés
- **Templates HTML** professionnels avec CSS inline
- **Rappels de contrats** 7 jours et 1 jour avant échéance
- **Confirmations de RDV** avec détails complets
- **Devis par email** avec PDF en pièce jointe

### SMS via Twilio
```php
// Configuration dans .env
TWILIO_SID=your_account_sid
TWILIO_TOKEN=your_auth_token
TWILIO_FROM=+**********

// Utilisation
$smsService = new SmsService();
$smsService->sendContractReminder($contract);
```

## 📄 Génération de PDF

### Devis Personnalisés
- **Design professionnel** avec logo et couleurs de marque
- **Informations client** complètes
- **Détails de simulation** avec calculs
- **Conditions générales** et mentions légales
- **Téléchargement sécurisé** via lien unique

## 🔧 Administration

### Dashboard Admin (`/admin`)
- **KPIs en temps réel** : leads, RDV, contrats expirant
- **Graphiques visuels** avec cartes colorées
- **Actions rapides** vers les sections principales
- **Activité récente** avec leads et RDV

### Gestion des Leads (`/admin/leads`)
- **Filtrage avancé** par statut, type, date
- **Recherche textuelle** dans nom, email, téléphone
- **Mise à jour de statut** avec notes
- **Pagination** pour performance

### Gestion des Contrats (`/admin/contracts`)
- **Monitoring des échéances** avec alertes visuelles
- **Filtres par statut** et type de contrat
- **Rappels automatiques** configurables
- **Historique des actions**

## 📅 Tâches Planifiées

### Configuration Cron
```bash
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

### Commandes Disponibles
```bash
# Rappels de contrats
php artisan contracts:send-reminders --days=7
php artisan contracts:send-reminders --days=1

# Nettoyage automatique
php artisan model:prune
```

## 🎨 Design System

### Couleurs Principales
- **Bleu principal** : #2563eb (blue-600)
- **Bleu foncé** : #1d4ed8 (blue-700)
- **Gris clair** : #f9fafb (gray-50)
- **Gris moyen** : #6b7280 (gray-500)

### Typographie
- **Police principale** : Inter (Google Fonts)
- **Tailles** : text-sm (14px), text-base (16px), text-lg (18px)
- **Poids** : font-medium (500), font-semibold (600), font-bold (700)

### Composants
- **Boutons** : Arrondis (rounded-lg), padding consistant
- **Cartes** : Ombre subtile (shadow-lg), bordures arrondies
- **Formulaires** : Focus states avec ring-2 ring-blue-500

## 🔒 Sécurité

### Authentification
- **Laravel Breeze** pour l'auth de base
- **Sessions sécurisées** avec CSRF protection
- **Middleware auth** pour les routes admin
- **Validation stricte** sur tous les formulaires

### Protection des Données
- **Conformité Loi 25** (Québec)
- **Mentions légales** complètes
- **Politique de confidentialité** détaillée
- **Droits utilisateurs** (accès, rectification, suppression)

## 📊 Base de Données

### Tables Principales
```sql
-- Leads de simulation
simulation_leads: id, first_name, last_name, email, phone, 
                 simulation_data (JSON), insurance_type, 
                 estimated_amount, status, notes

-- Contrats d'assurance
contracts: id, user_id, client_name, client_email, client_phone,
          contract_number, type, start_date, end_date, amount,
          status, contract_details (JSON), reminder_sent

-- Rendez-vous
appointments: id, client_name, client_email, client_phone,
             service_type, appointment_date, status, message,
             admin_notes, reminder_sent
```

### Relations
- **User hasMany Contracts** (optionnel)
- **Indexes optimisés** pour les requêtes fréquentes
- **JSON fields** pour flexibilité des données

## 🚀 Performance

### Optimisations
- **Eager loading** pour éviter N+1 queries
- **Pagination** sur toutes les listes
- **Cache des vues** en production
- **Compression des assets** avec Vite

### Monitoring
- **Logs structurés** pour debugging
- **Métriques de performance** dans le dashboard
- **Alertes automatiques** pour les erreurs

## 🧪 Tests

### Tests Inclus
- **Tests d'authentification** complets
- **Tests de fonctionnalités** principales
- **Tests unitaires** pour les modèles
- **Tests d'intégration** pour les APIs

### Commandes de Test
```bash
php artisan test                    # Tous les tests
php artisan test --coverage        # Avec couverture
php artisan test --parallel        # En parallèle
```

## 📱 Responsive Design

### Breakpoints Tailwind
- **sm** : 640px+ (tablettes)
- **md** : 768px+ (tablettes landscape)
- **lg** : 1024px+ (desktop)
- **xl** : 1280px+ (large desktop)

### Composants Adaptatifs
- **Navigation mobile** avec menu hamburger
- **Grilles responsives** qui s'adaptent
- **Formulaires optimisés** pour mobile
- **Cartes empilables** sur petits écrans

## 🔄 Workflow de Développement

### Git Workflow
```bash
git checkout -b feature/nouvelle-fonctionnalite
# Développement...
git add .
git commit -m "feat: ajouter nouvelle fonctionnalité"
git push origin feature/nouvelle-fonctionnalite
# Pull Request → Review → Merge
```

### Déploiement
```bash
# Optimisations production
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer install --optimize-autoloader --no-dev
npm run build
```

## 📈 Métriques Business

### KPIs Trackés
- **Taux de conversion** simulation → lead
- **Taux de prise de RDV** depuis simulation
- **Temps de réponse** aux leads
- **Taux de renouvellement** des contrats

### Rapports Disponibles
- **Dashboard temps réel** avec métriques clés
- **Exports CSV** des données
- **Graphiques de tendance** (à implémenter)
- **Alertes automatiques** pour objectifs

---

**Cette application représente une solution complète et moderne pour un assureur, avec toutes les fonctionnalités nécessaires pour générer des leads, gérer les clients et automatiser les processus métier.**
