@extends('layouts.main')

@section('title', 'Votre <PERSON> d\'Assurance - Assurance iA Montréal')
@section('description', 'Découvrez votre devis d\'assurance personnalisé et prenez rendez-vous avec nos experts.')

@section('content')
<!-- Progress Bar -->
<section class="bg-white border-b border-gray-200">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center justify-center">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Simulation</span>
                </div>
                <div class="w-16 h-1 bg-green-500"></div>
                <div class="flex items-center">
                    <div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Coordonnées</span>
                </div>
                <div class="w-16 h-1 bg-green-500"></div>
                <div class="flex items-center">
                    <div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold">
                        <i class="fas fa-check"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Résultat</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="mb-4">
            <i class="fas fa-check-circle text-6xl text-green-200"></i>
        </div>
        <h1 class="text-3xl font-bold mb-4">Félicitations {{ $lead->first_name }} !</h1>
        <p class="text-xl text-green-100">
            Votre devis d'assurance personnalisé est prêt
        </p>
    </div>
</section>

<!-- Results Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main Result Card -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Votre devis personnalisé</h2>
                <p class="text-gray-600">
                    Basé sur les informations que vous avez fournies
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Quote Details -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-900">
                        <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                        Détails de votre simulation
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Type d'assurance :</span>
                            <span class="font-medium">
                                @switch($simulationData['insurance_type'])
                                    @case('visa_visiteur')
                                        Assurance Visa Visiteur
                                        @break
                                    @case('assurance_vie')
                                        Assurance Vie
                                        @break
                                    @case('reer')
                                        REER
                                        @break
                                    @case('celi')
                                        CELI
                                        @break
                                @endswitch
                            </span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Âge :</span>
                            <span class="font-medium">{{ $simulationData['age'] }} ans</span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Montant de couverture :</span>
                            <span class="font-medium">{{ number_format($simulationData['coverage_amount'], 0, ',', ' ') }} $</span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">
                                @if($simulationData['insurance_type'] === 'visa_visiteur')
                                    Durée du séjour :
                                @else
                                    Durée :
                                @endif
                            </span>
                            <span class="font-medium">
                                {{ $simulationData['duration'] }}
                                @if($simulationData['insurance_type'] === 'visa_visiteur')
                                    jours
                                @else
                                    années
                                @endif
                            </span>
                        </div>

                        @if(isset($simulationData['country']) && $simulationData['country'])
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Pays de résidence :</span>
                            <span class="font-medium capitalize">{{ $simulationData['country'] }}</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Price Display -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-6">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold mb-4 text-indigo-900">
                            <i class="fas fa-calculator mr-2"></i>
                            Votre prime estimée
                        </h3>

                        <div class="mb-4">
                            <div class="text-4xl font-bold text-indigo-600 mb-2">
                                {{ number_format($simulationData['estimated_amount'], 2, ',', ' ') }} $
                            </div>
                            <div class="text-sm text-indigo-700">
                                @if($simulationData['insurance_type'] === 'visa_visiteur')
                                    Pour {{ $simulationData['duration'] }} jours de couverture
                                @elseif($simulationData['insurance_type'] === 'assurance_vie')
                                    Prime annuelle
                                @else
                                    Frais de gestion annuels
                                @endif
                            </div>
                        </div>

                        @if($simulationData['insurance_type'] === 'visa_visiteur')
                        <div class="bg-white rounded-lg p-3 mb-4">
                            <div class="text-sm text-gray-600">Coût par jour :</div>
                            <div class="text-lg font-semibold text-gray-900">
                                {{ number_format($simulationData['estimated_amount'] / $simulationData['duration'], 2, ',', ' ') }} $
                            </div>
                        </div>
                        @endif

                        <div class="text-xs text-blue-600 bg-blue-200 rounded-lg p-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Tarif indicatif - Devis final après étude personnalisée
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <a href="{{ route('simulation.pdf', $lead->id) }}" 
               class="bg-red-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-red-700 transition-colors text-center">
                <i class="fas fa-file-pdf mr-2"></i>
                Télécharger le PDF
            </a>

            <a href="{{ route('appointment.create') }}" 
               class="bg-blue-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center">
                <i class="fas fa-calendar-alt mr-2"></i>
                Prendre rendez-vous
            </a>

            <button onclick="shareQuote()" 
                    class="bg-green-600 text-white px-6 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                <i class="fas fa-share mr-2"></i>
                Partager
            </button>
        </div>

        <!-- Next Steps -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">
                <i class="fas fa-route mr-2 text-blue-600"></i>
                Prochaines étapes
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">1</span>
                    </div>
                    <h4 class="font-semibold mb-2">Consultation gratuite</h4>
                    <p class="text-gray-600 text-sm">
                        Un de nos experts vous contactera dans les 24h pour affiner votre devis
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-green-600">2</span>
                    </div>
                    <h4 class="font-semibold mb-2">Personnalisation</h4>
                    <p class="text-gray-600 text-sm">
                        Nous ajustons votre couverture selon vos besoins spécifiques
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-purple-600">3</span>
                    </div>
                    <h4 class="font-semibold mb-2">Souscription</h4>
                    <p class="text-gray-600 text-sm">
                        Finalisation rapide de votre contrat et mise en place de la protection
                    </p>
                </div>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="bg-blue-600 text-white rounded-2xl p-8 mt-8">
            <div class="text-center">
                <h3 class="text-xl font-bold mb-4">Une question ? Contactez-nous !</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <i class="fas fa-phone text-2xl mb-2"></i>
                        <div class="font-semibold">(*************</div>
                        <div class="text-sm text-blue-200">Lun-Ven: 9h-17h</div>
                    </div>
                    <div>
                        <i class="fas fa-envelope text-2xl mb-2"></i>
                        <div class="font-semibold"><EMAIL></div>
                        <div class="text-sm text-blue-200">Réponse sous 2h</div>
                    </div>
                    <div>
                        <i class="fas fa-comments text-2xl mb-2"></i>
                        <div class="font-semibold">Chat en ligne</div>
                        <div class="text-sm text-blue-200">Support immédiat</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function shareQuote() {
    if (navigator.share) {
        navigator.share({
            title: 'Mon devis d\'assurance - Assurance iA Montréal',
            text: 'J\'ai obtenu mon devis d\'assurance personnalisé avec Assurance iA Montréal',
            url: window.location.href
        });
    } else {
        // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Lien copié dans le presse-papiers !');
        });
    }
}
</script>
@endpush
@endsection
