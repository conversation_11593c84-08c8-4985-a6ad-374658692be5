@extends('layouts.main')

@section('title', 'Simulation d\'Assurance Gratuite - Assurance iA Montréal')
@section('description', 'Obtenez votre devis d\'assurance personnalisé en quelques minutes. Simulation gratuite pour visa visiteur, assurance vie, REER et CELI.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-indigo-600 to-purple-800 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">Simulation d'Assurance Gratuite</h1>
        <p class="text-xl text-indigo-100">
            Obtenez votre devis personnalisé en quelques minutes
        </p>
    </div>
</section>

<!-- Simulation Form -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {{ session('error') }}
            </div>
        @endif

        <div class="bg-white rounded-2xl shadow-lg p-8">
            <form action="{{ route('simulation.calculate') }}" method="POST" id="simulation-form">
                @csrf

                <!-- Type de simulation -->
                <div class="mb-8">
                    <label class="block text-lg font-semibold text-gray-900 mb-6">
                        <i class="fas fa-calculator mr-2 text-indigo-600"></i>
                        Type de simulation
                    </label>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($simulationTypes as $key => $label)
                        <label class="relative">
                            <input type="radio" name="simulation_type" value="{{ $key }}" class="sr-only peer" {{ $selectedType === $key ? 'checked' : '' }} required>
                            <div class="border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-{{ $key === 'assurance' ? 'indigo' : 'green' }}-300 peer-checked:border-{{ $key === 'assurance' ? 'indigo' : 'green' }}-600 peer-checked:bg-{{ $key === 'assurance' ? 'indigo' : 'green' }}-50 transition-colors">
                                <div class="text-center">
                                    @if($key === 'assurance')
                                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="fas fa-shield-alt text-indigo-600 text-2xl"></i>
                                        </div>
                                        <h3 class="font-semibold text-gray-900 mb-2">Assurances</h3>
                                        <p class="text-sm text-gray-600">Assurance vie, hypothécaire, voyage, invalidité...</p>
                                    @else
                                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="fas fa-piggy-bank text-green-600 text-2xl"></i>
                                        </div>
                                        <h3 class="font-semibold text-gray-900 mb-2">Épargne et Retraite</h3>
                                        <p class="text-sm text-gray-600">REER, CELI, FERR, placements garantis...</p>
                                    @endif
                                </div>
                            </div>
                        </label>
                        @endforeach
                    </div>

                    @error('simulation_type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Informations personnelles -->
                <div class="mb-8">
                    <label for="age" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-birthday-cake mr-2 text-indigo-600"></i>
                        Âge
                    </label>
                    <input type="number" id="age" name="age" min="18" max="100"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Ex: 35" value="{{ old('age') }}" required>
                    @error('age')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Montant de couverture -->
                <div class="mb-8">
                    <label for="coverage_amount" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-dollar-sign mr-2 text-indigo-600"></i>
                        <span id="coverage-label">Montant de couverture souhaité (CAD)</span>
                    </label>
                    <select id="coverage_amount" name="coverage_amount"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                        <option value="">Sélectionnez un montant</option>
                        <!-- Options d'assurance -->
                        <optgroup label="Assurances" class="assurance-options">
                            <option value="50000">50 000 $ - Protection de base</option>
                            <option value="100000">100 000 $ - Protection standard</option>
                            <option value="250000">250 000 $ - Protection étendue</option>
                            <option value="500000">500 000 $ - Protection premium</option>
                            <option value="1000000">1 000 000 $ - Protection maximale</option>
                        </optgroup>
                        <!-- Options d'épargne -->
                        <optgroup label="Épargne et Retraite" class="epargne-options">
                            <option value="5000">5 000 $ - Contribution annuelle</option>
                            <option value="10000">10 000 $ - Contribution annuelle</option>
                            <option value="15000">15 000 $ - Contribution annuelle</option>
                            <option value="25000">25 000 $ - Contribution annuelle</option>
                            <option value="50000">50 000 $ - Contribution annuelle</option>
                        </optgroup>
                    </select>
                    @error('coverage_amount')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Durée -->
                <div class="mb-8">
                    <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-alt mr-2 text-indigo-600"></i>
                        <span id="duration-label">Durée (jours)</span>
                    </label>
                    <input type="number" id="duration" name="duration" min="1"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Ex: 30" value="{{ old('duration') }}" required>
                    @error('duration')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-calculator mr-2"></i>
                        Calculer ma prime
                    </button>
                </div>
            </form>
        </div>

        <!-- Information Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div class="bg-white rounded-lg p-6 shadow-md">
                <div class="text-center">
                    <i class="fas fa-clock text-indigo-600 text-3xl mb-3"></i>
                    <h3 class="font-semibold mb-2">Rapide</h3>
                    <p class="text-gray-600 text-sm">Résultat en moins de 2 minutes</p>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-md">
                <div class="text-center">
                    <i class="fas fa-gift text-green-600 text-3xl mb-3"></i>
                    <h3 class="font-semibold mb-2">Gratuit</h3>
                    <p class="text-gray-600 text-sm">Aucun engagement, simulation 100% gratuite</p>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-md">
                <div class="text-center">
                    <i class="fas fa-user-tie text-purple-600 text-3xl mb-3"></i>
                    <h3 class="font-semibold mb-2">Expert</h3>
                    <p class="text-gray-600 text-sm">Conseils personnalisés de nos experts</p>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script chargé');
    const simulationTypeInputs = document.querySelectorAll('input[name="simulation_type"]');
    const coverageAmountSelect = document.getElementById('coverage_amount');
    const coverageLabel = document.getElementById('coverage-label');
    const durationInput = document.getElementById('duration');
    const durationLabel = document.getElementById('duration-label');

    console.log('Éléments trouvés:', {
        simulationTypeInputs: simulationTypeInputs.length,
        coverageAmountSelect: !!coverageAmountSelect,
        coverageLabel: !!coverageLabel,
        durationInput: !!durationInput,
        durationLabel: !!durationLabel
    });

    const coverageOptions = {
        assurance: [
            { value: 50000, text: '50 000 $ - Protection de base' },
            { value: 100000, text: '100 000 $ - Protection standard' },
            { value: 250000, text: '250 000 $ - Protection étendue' },
            { value: 500000, text: '500 000 $ - Protection premium' },
            { value: 1000000, text: '1 000 000 $ - Protection maximale' }
        ],
        epargne: [
            { value: 5000, text: '5 000 $ - Contribution annuelle' },
            { value: 10000, text: '10 000 $ - Contribution annuelle' },
            { value: 15000, text: '15 000 $ - Contribution annuelle' },
            { value: 25000, text: '25 000 $ - Contribution annuelle' },
            { value: 50000, text: '50 000 $ - Contribution annuelle' }
        ]
    };

    function updateFormFields(simulationType) {
        // Update coverage options
        coverageAmountSelect.innerHTML = '<option value="">Sélectionnez un montant</option>';
        if (coverageOptions[simulationType]) {
            coverageOptions[simulationType].forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                coverageAmountSelect.appendChild(optionElement);
            });
        }

        // Update labels and placeholders
        if (simulationType === 'assurance') {
            coverageLabel.textContent = 'Montant de couverture souhaité (CAD)';
            durationLabel.textContent = 'Durée de protection (années)';
            durationInput.placeholder = 'Ex: 20';
        } else if (simulationType === 'epargne') {
            coverageLabel.textContent = 'Montant de contribution annuelle (CAD)';
            durationLabel.textContent = 'Horizon de placement (années)';
            durationInput.placeholder = 'Ex: 25';
        }
    }

    simulationTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.checked) {
                updateFormFields(this.value);
            }
        });
    });

    // Initialiser avec le type sélectionné
    const selectedInput = document.querySelector('input[name="simulation_type"]:checked');
    if (selectedInput) {
        updateFormFields(selectedInput.value);
    }
});
</script>
@endpush
@endsection
