<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis d'Assurance - {{ $lead->full_name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        .subtitle {
            color: #6b7280;
            font-size: 14px;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            background: #f3f4f6;
            padding: 10px 15px;
            font-weight: bold;
            color: #1f2937;
            border-left: 4px solid #2563eb;
            margin-bottom: 15px;
        }
        .info-grid {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            padding: 8px 0;
            font-weight: bold;
            width: 40%;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-value {
            display: table-cell;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .price-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .price {
            font-size: 32px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        .price-label {
            color: #1e40af;
            font-size: 14px;
        }
        .features {
            background: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .features ul {
            margin: 0;
            padding-left: 20px;
        }
        .features li {
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }
        .contact-info {
            background: #eff6ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .important {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .important-title {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🛡️ Assurance iA Montréal</div>
        <div class="subtitle">Affilié à iA Groupe financier</div>
        <h1 style="margin: 20px 0 10px 0; color: #1f2937;">Devis d'Assurance Personnalisé</h1>
        <p style="margin: 0; color: #6b7280;">Généré le {{ now()->format('d/m/Y à H:i') }}</p>
    </div>

    <div class="section">
        <div class="section-title">👤 Informations Client</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Nom complet :</div>
                <div class="info-value">{{ $lead->full_name }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Email :</div>
                <div class="info-value">{{ $lead->email }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Téléphone :</div>
                <div class="info-value">{{ $lead->phone }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">Date de simulation :</div>
                <div class="info-value">{{ $lead->created_at->format('d/m/Y à H:i') }}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">📋 Détails de la Simulation</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Type d'assurance :</div>
                <div class="info-value">
                    @switch($lead->insurance_type)
                        @case('visa_visiteur')
                            Assurance Visa Visiteur
                            @break
                        @case('assurance_vie')
                            Assurance Vie
                            @break
                        @case('reer')
                            REER (Régime enregistré d'épargne-retraite)
                            @break
                        @case('celi')
                            CELI (Compte d'épargne libre d'impôt)
                            @break
                        @default
                            {{ ucfirst(str_replace('_', ' ', $lead->insurance_type)) }}
                    @endswitch
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">Âge :</div>
                <div class="info-value">{{ $lead->simulation_data['age'] }} ans</div>
            </div>
            <div class="info-row">
                <div class="info-label">Montant de couverture :</div>
                <div class="info-value">{{ number_format($lead->simulation_data['coverage_amount'], 0, ',', ' ') }} $ CAD</div>
            </div>
            <div class="info-row">
                <div class="info-label">Durée :</div>
                <div class="info-value">
                    {{ $lead->simulation_data['duration'] }}
                    @if($lead->insurance_type === 'visa_visiteur')
                        jours
                    @else
                        années
                    @endif
                </div>
            </div>
            @if(isset($lead->simulation_data['country']) && $lead->simulation_data['country'])
            <div class="info-row">
                <div class="info-label">Pays de résidence :</div>
                <div class="info-value">{{ ucfirst($lead->simulation_data['country']) }}</div>
            </div>
            @endif
        </div>
    </div>

    <div class="price-box">
        <div class="price">{{ number_format($lead->estimated_amount, 2, ',', ' ') }} $</div>
        <div class="price-label">
            @if($lead->insurance_type === 'visa_visiteur')
                Prime totale pour {{ $lead->simulation_data['duration'] }} jours
            @elseif($lead->insurance_type === 'assurance_vie')
                Prime annuelle
            @else
                Frais de gestion annuels
            @endif
        </div>
    </div>

    @if($lead->insurance_type === 'visa_visiteur')
    <div class="section">
        <div class="section-title">✈️ Couverture Visa Visiteur</div>
        <div class="features">
            <h4 style="margin-top: 0; color: #1f2937;">Votre protection inclut :</h4>
            <ul>
                <li>Soins médicaux d'urgence jusqu'à {{ number_format($lead->simulation_data['coverage_amount'], 0, ',', ' ') }} $</li>
                <li>Hospitalisation et chirurgie d'urgence</li>
                <li>Médicaments prescrits</li>
                <li>Services d'ambulance</li>
                <li>Rapatriement médical d'urgence</li>
                <li>Assistance voyage 24h/24, 7j/7</li>
                <li>Couverture dentaire d'urgence (jusqu'à 3 000 $)</li>
            </ul>
        </div>
        
        <div style="margin-top: 15px;">
            <strong>Coût par jour :</strong> {{ number_format($lead->estimated_amount / $lead->simulation_data['duration'], 2, ',', ' ') }} $ CAD
        </div>
    </div>
    @endif

    @if($lead->insurance_type === 'assurance_vie')
    <div class="section">
        <div class="section-title">❤️ Assurance Vie</div>
        <div class="features">
            <h4 style="margin-top: 0; color: #1f2937;">Avantages de votre protection :</h4>
            <ul>
                <li>Capital décès garanti de {{ number_format($lead->simulation_data['coverage_amount'], 0, ',', ' ') }} $</li>
                <li>Protection de votre famille</li>
                <li>Primes nivelées (ne changent pas avec l'âge)</li>
                <li>Possibilité d'ajouter des avenants</li>
                <li>Valeur de rachat (selon le type choisi)</li>
                <li>Examen médical simplifié</li>
            </ul>
        </div>
    </div>
    @endif

    @if(in_array($lead->insurance_type, ['reer', 'celi']))
    <div class="section">
        <div class="section-title">
            @if($lead->insurance_type === 'reer')
                🏦 REER - Régime Enregistré d'Épargne-Retraite
            @else
                💰 CELI - Compte d'Épargne Libre d'Impôt
            @endif
        </div>
        <div class="features">
            <h4 style="margin-top: 0; color: #1f2937;">Avantages fiscaux :</h4>
            <ul>
                @if($lead->insurance_type === 'reer')
                    <li>Déduction fiscale immédiate</li>
                    <li>Croissance à l'abri de l'impôt</li>
                    <li>Report d'impôt jusqu'au retrait</li>
                    <li>Possibilité de RAP et REEP</li>
                @else
                    <li>Retraits libres d'impôt</li>
                    <li>Croissance libre d'impôt</li>
                    <li>Aucun âge limite</li>
                    <li>Droits de cotisation récupérables</li>
                @endif
                <li>Gestion professionnelle de portefeuille</li>
                <li>Diversification des investissements</li>
            </ul>
        </div>
    </div>
    @endif

    <div class="important">
        <div class="important-title">⚠️ Important</div>
        <p style="margin: 0;">
            Ce devis est une estimation basée sur les informations fournies. Le tarif final peut varier 
            après étude complète de votre dossier. Cette offre est valable 30 jours à compter de la date 
            de génération.
        </p>
    </div>

    <div class="contact-info">
        <h4 style="margin-top: 0; color: #1e40af;">📞 Prochaines étapes</h4>
        <p style="margin-bottom: 10px;">
            <strong>Contactez-nous pour finaliser votre protection :</strong>
        </p>
        <ul style="margin: 0; padding-left: 20px;">
            <li>Téléphone : (*************</li>
            <li>Email : <EMAIL></li>
            <li>Prenez rendez-vous en ligne sur notre site web</li>
        </ul>
    </div>

    <div class="section">
        <div class="section-title">🤝 Pourquoi choisir Assurance iA Montréal ?</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-row;">
                <div style="display: table-cell; width: 50%; padding-right: 15px; vertical-align: top;">
                    <h4 style="color: #1f2937; margin-top: 0;">✅ Expertise reconnue</h4>
                    <p style="font-size: 14px; margin-bottom: 15px;">Plus de 20 ans d'expérience dans le domaine de l'assurance.</p>
                    
                    <h4 style="color: #1f2937;">🏆 Partenaire de confiance</h4>
                    <p style="font-size: 14px; margin-bottom: 0;">Affilié à iA Groupe financier, leader canadien de l'assurance.</p>
                </div>
                <div style="display: table-cell; width: 50%; padding-left: 15px; vertical-align: top;">
                    <h4 style="color: #1f2937; margin-top: 0;">👥 Service personnalisé</h4>
                    <p style="font-size: 14px; margin-bottom: 15px;">Accompagnement sur mesure pour trouver la solution qui vous convient.</p>
                    
                    <h4 style="color: #1f2937;">⚡ Réactivité</h4>
                    <p style="font-size: 14px; margin-bottom: 0;">Réponse rapide et traitement efficace de vos demandes.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p style="text-align: center; margin-bottom: 15px;">
            <strong>Assurance iA Montréal</strong> - Affilié à iA Groupe financier<br>
            Montréal, Québec, Canada
        </p>
        <p style="text-align: center; margin: 0;">
            Ce document est confidentiel et destiné exclusivement à {{ $lead->full_name }}.<br>
            Généré automatiquement le {{ now()->format('d/m/Y à H:i') }} - Référence : {{ $lead->id }}
        </p>
    </div>
</body>
</html>
