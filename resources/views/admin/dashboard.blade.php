@extends('layouts.main')

@section('title', 'Dashboard Admin - Assurance iA Montréal')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Dashboard Admin</h1>
                    <p class="text-gray-600">Vue d'ensemble de votre activité</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="bg-blue-100 rounded-full p-3">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-blue-600">Leads aujourd'hui</p>
                                <p class="text-2xl font-bold text-blue-900">{{ $stats['leads_today'] }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="bg-green-100 rounded-full p-3">
                                <i class="fas fa-chart-line text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-green-600">Leads cette semaine</p>
                                <p class="text-2xl font-bold text-green-900">{{ $stats['leads_week'] }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 rounded-full p-3">
                                <i class="fas fa-calendar text-yellow-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-yellow-600">RDV aujourd'hui</p>
                                <p class="text-2xl font-bold text-yellow-900">{{ $stats['appointments_today'] }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="bg-red-100 rounded-full p-3">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-red-600">Contrats expirant</p>
                                <p class="text-2xl font-bold text-red-900">{{ $stats['contracts_expiring'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <a href="{{ route('admin.leads') }}" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-user-plus text-blue-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Gérer les Leads</h3>
                                <p class="text-sm text-gray-600">Voir et qualifier les nouveaux leads</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('admin.appointments') }}" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-check text-green-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Rendez-vous</h3>
                                <p class="text-sm text-gray-600">Gérer les rendez-vous clients</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('admin.contracts') }}" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-file-contract text-purple-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Contrats</h3>
                                <p class="text-sm text-gray-600">Suivre les échéances de contrats</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('admin.services.index') }}" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-indigo-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Services</h3>
                                <p class="text-sm text-gray-600">Gérer les services d'assurance</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('admin.settings.index') }}" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-cog text-gray-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Paramètres</h3>
                                <p class="text-sm text-gray-600">Configuration du site</p>
                            </div>
                        </div>
                    </a>

                    <a href="{{ route('home') }}" target="_blank" class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-center">
                            <i class="fas fa-external-link-alt text-teal-600 text-2xl mr-4"></i>
                            <div>
                                <h3 class="font-semibold text-gray-900">Voir le Site</h3>
                                <p class="text-sm text-gray-600">Aperçu du site public</p>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Leads -->
                    <div class="bg-white border border-gray-200 rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Leads Récents</h3>
                        </div>
                        <div class="p-6">
                            @forelse($recentLeads as $lead)
                            <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $lead->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $lead->insurance_type)) }}</p>
                                    <p class="text-xs text-gray-500">{{ $lead->created_at->diffForHumans() }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($lead->status === 'new') bg-blue-100 text-blue-800
                                        @elseif($lead->status === 'contacted') bg-yellow-100 text-yellow-800
                                        @elseif($lead->status === 'converted') bg-green-100 text-green-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($lead->status) }}
                                    </span>
                                    <p class="text-sm font-medium text-gray-900 mt-1">{{ number_format($lead->estimated_amount, 0) }} $</p>
                                </div>
                            </div>
                            @empty
                            <p class="text-gray-500 text-center py-4">Aucun lead récent</p>
                            @endforelse
                        </div>
                    </div>

                    <!-- Upcoming Appointments -->
                    <div class="bg-white border border-gray-200 rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Prochains Rendez-vous</h3>
                        </div>
                        <div class="p-6">
                            @forelse($upcomingAppointments as $appointment)
                            <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $appointment->client_name }}</p>
                                    <p class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $appointment->service_type)) }}</p>
                                    <p class="text-xs text-gray-500">{{ $appointment->appointment_date->format('d/m/Y à H:i') }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($appointment->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($appointment->status === 'confirmed') bg-green-100 text-green-800
                                        @elseif($appointment->status === 'completed') bg-blue-100 text-blue-800
                                        @else bg-red-100 text-red-800 @endif">
                                        {{ ucfirst($appointment->status) }}
                                    </span>
                                </div>
                            </div>
                            @empty
                            <p class="text-gray-500 text-center py-4">Aucun rendez-vous à venir</p>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Expiring Contracts -->
                @if($expiringContracts->count() > 0)
                <div class="mt-6">
                    <div class="bg-red-50 border border-red-200 rounded-lg">
                        <div class="px-6 py-4 border-b border-red-200">
                            <h3 class="text-lg font-semibold text-red-900">⚠️ Contrats Expirant Bientôt</h3>
                        </div>
                        <div class="p-6">
                            @foreach($expiringContracts as $contract)
                            <div class="flex items-center justify-between py-3 border-b border-red-100 last:border-b-0">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $contract->client_name }}</p>
                                    <p class="text-sm text-gray-600">Contrat #{{ $contract->contract_number }}</p>
                                    <p class="text-xs text-red-600">Expire le {{ $contract->end_date->format('d/m/Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ number_format($contract->amount, 0) }} $</p>
                                    <p class="text-xs text-red-600">{{ $contract->end_date->diffInDays(now()) }} jour(s)</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
