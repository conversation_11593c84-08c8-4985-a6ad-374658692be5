@extends('layouts.main')

@section('title', 'Gestion des Rendez-vous - Admin')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Gestion des Rendez-vous</h1>
                    <p class="text-gray-600 mt-2"><PERSON><PERSON>rez et confirmez les rendez-vous clients</p>
                </div>
                <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Retour au Dashboard
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <form method="GET" action="{{ route('admin.appointments') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les statuts</option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>En attente</option>
                        <option value="confirmed" {{ request('status') === 'confirmed' ? 'selected' : '' }}>Confirmé</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Terminé</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Annulé</option>
                    </select>
                </div>

                <div>
                    <label for="service_type" class="block text-sm font-medium text-gray-700 mb-1">Type de service</label>
                    <select name="service_type" id="service_type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les services</option>
                        <option value="visa_visiteur" {{ request('service_type') === 'visa_visiteur' ? 'selected' : '' }}>Visa Visiteur</option>
                        <option value="assurance_vie" {{ request('service_type') === 'assurance_vie' ? 'selected' : '' }}>Assurance Vie</option>
                        <option value="reer" {{ request('service_type') === 'reer' ? 'selected' : '' }}>REER</option>
                        <option value="celi" {{ request('service_type') === 'celi' ? 'selected' : '' }}>CELI</option>
                        <option value="consultation_generale" {{ request('service_type') === 'consultation_generale' ? 'selected' : '' }}>Consultation générale</option>
                        <option value="renouvellement" {{ request('service_type') === 'renouvellement' ? 'selected' : '' }}>Renouvellement</option>
                    </select>
                </div>

                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" name="date" id="date" value="{{ request('date') }}" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Filtrer
                    </button>
                </div>
            </form>
        </div>

        <!-- Quick Filters -->
        <div class="mb-6 flex flex-wrap gap-2">
            <a href="{{ route('admin.appointments', ['date' => now()->format('Y-m-d')]) }}"
               class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm hover:bg-indigo-200 transition-colors">
                <i class="fas fa-calendar-day mr-1"></i>
                Aujourd'hui
            </a>
            <a href="{{ route('admin.appointments', ['status' => 'pending']) }}" 
               class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm hover:bg-yellow-200 transition-colors">
                <i class="fas fa-clock mr-1"></i>
                En attente
            </a>
            <a href="{{ route('admin.appointments', ['status' => 'confirmed']) }}" 
               class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm hover:bg-green-200 transition-colors">
                <i class="fas fa-check mr-1"></i>
                Confirmés
            </a>
        </div>

        <!-- Results Summary -->
        <div class="mb-6">
            <p class="text-gray-600">
                <strong>{{ $appointments->total() }}</strong> rendez-vous trouvé(s)
                @if(request()->hasAny(['status', 'service_type', 'date']))
                    - <a href="{{ route('admin.appointments') }}" class="text-blue-600 hover:text-blue-700">Effacer les filtres</a>
                @endif
            </p>
        </div>

        <!-- Appointments Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            @if($appointments->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Heure</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($appointments as $appointment)
                            <tr class="hover:bg-gray-50 {{ $appointment->appointment_date->isToday() ? 'bg-blue-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $appointment->client_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $appointment->client_email }}</div>
                                        <div class="text-sm text-gray-500">{{ $appointment->client_phone }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($appointment->service_type === 'visa_visiteur') bg-blue-100 text-blue-800
                                        @elseif($appointment->service_type === 'assurance_vie') bg-red-100 text-red-800
                                        @elseif($appointment->service_type === 'reer') bg-green-100 text-green-800
                                        @elseif($appointment->service_type === 'celi') bg-yellow-100 text-yellow-800
                                        @elseif($appointment->service_type === 'consultation_generale') bg-purple-100 text-purple-800
                                        @else bg-orange-100 text-orange-800 @endif">
                                        @switch($appointment->service_type)
                                            @case('visa_visiteur') Visa Visiteur @break
                                            @case('assurance_vie') Assurance Vie @break
                                            @case('reer') REER @break
                                            @case('celi') CELI @break
                                            @case('consultation_generale') Consultation @break
                                            @case('renouvellement') Renouvellement @break
                                            @default {{ ucfirst(str_replace('_', ' ', $appointment->service_type)) }}
                                        @endswitch
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $appointment->appointment_date->format('d/m/Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $appointment->appointment_date->format('H:i') }}</div>
                                    @if($appointment->appointment_date->isToday())
                                        <div class="text-xs text-blue-600 font-medium">Aujourd'hui</div>
                                    @elseif($appointment->appointment_date->isTomorrow())
                                        <div class="text-xs text-orange-600 font-medium">Demain</div>
                                    @elseif($appointment->appointment_date->isPast())
                                        <div class="text-xs text-red-600 font-medium">Passé</div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($appointment->status === 'pending') bg-yellow-100 text-yellow-800
                                        @elseif($appointment->status === 'confirmed') bg-green-100 text-green-800
                                        @elseif($appointment->status === 'completed') bg-blue-100 text-blue-800
                                        @else bg-red-100 text-red-800 @endif">
                                        @switch($appointment->status)
                                            @case('pending') En attente @break
                                            @case('confirmed') Confirmé @break
                                            @case('completed') Terminé @break
                                            @case('cancelled') Annulé @break
                                        @endswitch
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    @if($appointment->message)
                                        <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $appointment->message }}">
                                            {{ $appointment->message }}
                                        </div>
                                    @else
                                        <span class="text-gray-400 text-sm">Aucun message</span>
                                    @endif
                                    @if($appointment->admin_notes)
                                        <div class="text-xs text-blue-600 mt-1">
                                            <i class="fas fa-sticky-note mr-1"></i>
                                            Notes admin
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="openUpdateModal({{ $appointment->id }}, '{{ $appointment->status }}', '{{ addslashes($appointment->admin_notes ?? '') }}')" 
                                                class="text-blue-600 hover:text-blue-900" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="mailto:{{ $appointment->client_email }}" class="text-green-600 hover:text-green-900" title="Envoyer un email">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="tel:{{ $appointment->client_phone }}" class="text-purple-600 hover:text-purple-900" title="Appeler">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                        @if($appointment->message)
                                            <button onclick="showMessage('{{ addslashes($appointment->message) }}')" class="text-orange-600 hover:text-orange-900" title="Voir le message">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $appointments->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun rendez-vous trouvé</h3>
                    <p class="text-gray-500">Aucun rendez-vous ne correspond à vos critères de recherche.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Update Modal -->
<div id="updateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="updateForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Mettre à jour le rendez-vous</h3>
                    
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
                        <select name="status" id="modalStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="pending">En attente</option>
                            <option value="confirmed">Confirmé</option>
                            <option value="completed">Terminé</option>
                            <option value="cancelled">Annulé</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Notes admin</label>
                        <textarea name="admin_notes" id="modalNotes" rows="3" 
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Ajouter des notes..."></textarea>
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button type="button" onclick="closeUpdateModal()" 
                            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Mettre à jour
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Message Modal -->
<div id="messageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Message du client</h3>
                <div id="messageContent" class="text-gray-700 bg-gray-50 p-4 rounded-lg"></div>
            </div>
            <div class="px-6 py-4 bg-gray-50 flex justify-end">
                <button type="button" onclick="closeMessageModal()" 
                        class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openUpdateModal(appointmentId, status, notes) {
    document.getElementById('updateForm').action = `/admin/appointments/${appointmentId}`;
    document.getElementById('modalStatus').value = status;
    document.getElementById('modalNotes').value = notes;
    document.getElementById('updateModal').classList.remove('hidden');
}

function closeUpdateModal() {
    document.getElementById('updateModal').classList.add('hidden');
}

function showMessage(message) {
    document.getElementById('messageContent').textContent = message;
    document.getElementById('messageModal').classList.remove('hidden');
}

function closeMessageModal() {
    document.getElementById('messageModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('updateModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUpdateModal();
    }
});

document.getElementById('messageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeMessageModal();
    }
});
</script>
@endpush
@endsection
