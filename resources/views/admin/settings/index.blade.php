@extends('layouts.main')

@section('title', 'Paramètres du Site - Admin')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Paramètres du Site</h1>
                    <p class="text-gray-600 mt-2">Configurez les informations et paramètres de votre site</p>
                </div>
                <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Retour au Dashboard
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <form action="{{ route('admin.settings.update') }}" method="POST">
            @csrf
            @method('PUT')

            @foreach($settings as $groupName => $groupSettings)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 capitalize">
                        @switch($groupName)
                            @case('general')
                                <i class="fas fa-globe mr-2 text-blue-600"></i>
                                Informations Générales
                                @break
                            @case('contact')
                                <i class="fas fa-address-book mr-2 text-green-600"></i>
                                Coordonnées
                                @break
                            @case('simulation')
                                <i class="fas fa-calculator mr-2 text-purple-600"></i>
                                Paramètres de Simulation
                                @break
                            @case('social')
                                <i class="fas fa-share-alt mr-2 text-pink-600"></i>
                                Réseaux Sociaux
                                @break
                            @case('seo')
                                <i class="fas fa-search mr-2 text-orange-600"></i>
                                SEO & Analytics
                                @break
                            @default
                                <i class="fas fa-cog mr-2 text-gray-600"></i>
                                {{ ucfirst($groupName) }}
                        @endswitch
                    </h3>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($groupSettings as $setting)
                        <div class="space-y-2">
                            <label for="{{ $setting->key }}" class="block text-sm font-medium text-gray-700">
                                {{ $setting->label }}
                                @if($setting->description)
                                    <span class="text-xs text-gray-500 block">{{ $setting->description }}</span>
                                @endif
                            </label>

                            @switch($setting->type)
                                @case('textarea')
                                    <textarea name="{{ $setting->key }}" id="{{ $setting->key }}" rows="3"
                                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ $setting->value }}</textarea>
                                    @break

                                @case('number')
                                    <input type="number" name="{{ $setting->key }}" id="{{ $setting->key }}" 
                                           value="{{ $setting->value }}" step="0.01"
                                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    @break

                                @case('boolean')
                                    <div class="flex items-center">
                                        <input type="checkbox" name="{{ $setting->key }}" id="{{ $setting->key }}" 
                                               value="1" {{ $setting->value ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <label for="{{ $setting->key }}" class="ml-2 text-sm text-gray-600">Activé</label>
                                    </div>
                                    @break

                                @default
                                    <input type="text" name="{{ $setting->key }}" id="{{ $setting->key }}" 
                                           value="{{ $setting->value }}"
                                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            @endswitch

                            @error($setting->key)
                                <p class="text-red-500 text-sm">{{ $message }}</p>
                            @enderror
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endforeach

            <!-- Save Button -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('admin.dashboard') }}" class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors">
                    Annuler
                </a>
                <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Enregistrer les Paramètres
                </button>
            </div>
        </form>

        <!-- Advanced Settings -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-900 mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Paramètres Avancés
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-yellow-900 mb-2">Calculs de Simulation</h4>
                    <p class="text-sm text-yellow-800 mb-3">
                        Les taux de base sont utilisés pour calculer automatiquement les primes d'assurance dans le simulateur.
                    </p>
                    <ul class="text-xs text-yellow-700 space-y-1">
                        <li>• <strong>Visa visiteur :</strong> Taux par jour par 1000$ de couverture</li>
                        <li>• <strong>Assurance vie :</strong> Taux par mois par 1000$ de couverture</li>
                        <li>• <strong>REER/CELI :</strong> Pourcentage de frais de gestion annuel</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-yellow-900 mb-2">SEO & Analytics</h4>
                    <p class="text-sm text-yellow-800 mb-3">
                        Configurez le suivi Google Analytics et optimisez le référencement de votre site.
                    </p>
                    <ul class="text-xs text-yellow-700 space-y-1">
                        <li>• <strong>Google Analytics :</strong> Format GA4 (G-XXXXXXXXXX)</li>
                        <li>• <strong>Mots-clés :</strong> Séparez par des virgules</li>
                        <li>• <strong>Description :</strong> Maximum 160 caractères</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('admin.services.index') }}" class="bg-blue-100 text-blue-800 px-4 py-3 rounded-lg hover:bg-blue-200 transition-colors text-center">
                <i class="fas fa-shield-alt mr-2"></i>
                Gérer les Services
            </a>
            <a href="{{ route('home') }}" target="_blank" class="bg-green-100 text-green-800 px-4 py-3 rounded-lg hover:bg-green-200 transition-colors text-center">
                <i class="fas fa-external-link-alt mr-2"></i>
                Voir le Site Public
            </a>
            <button onclick="clearCache()" class="bg-orange-100 text-orange-800 px-4 py-3 rounded-lg hover:bg-orange-200 transition-colors">
                <i class="fas fa-sync mr-2"></i>
                Vider le Cache
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function clearCache() {
    if (confirm('Êtes-vous sûr de vouloir vider le cache ? Cela peut temporairement ralentir le site.')) {
        fetch('/admin/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Cache vidé avec succès !');
            } else {
                alert('Erreur lors du vidage du cache.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors du vidage du cache.');
        });
    }
}
</script>
@endpush
@endsection
