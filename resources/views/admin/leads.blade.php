@extends('layouts.main')

@section('title', 'Gestion des Leads - Admin')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Gestion des Leads</h1>
                    <p class="text-gray-600 mt-2"><PERSON><PERSON><PERSON> et qualifiez vos leads de simulation</p>
                </div>
                <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Retour au Dashboard
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <form method="GET" action="{{ route('admin.leads') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les statuts</option>
                        <option value="new" {{ request('status') === 'new' ? 'selected' : '' }}>Nouveau</option>
                        <option value="contacted" {{ request('status') === 'contacted' ? 'selected' : '' }}>Contacté</option>
                        <option value="converted" {{ request('status') === 'converted' ? 'selected' : '' }}>Converti</option>
                        <option value="lost" {{ request('status') === 'lost' ? 'selected' : '' }}>Perdu</option>
                    </select>
                </div>

                <div>
                    <label for="insurance_type" class="block text-sm font-medium text-gray-700 mb-1">Type d'assurance</label>
                    <select name="insurance_type" id="insurance_type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les types</option>
                        <option value="visa_visiteur" {{ request('insurance_type') === 'visa_visiteur' ? 'selected' : '' }}>Visa Visiteur</option>
                        <option value="assurance_vie" {{ request('insurance_type') === 'assurance_vie' ? 'selected' : '' }}>Assurance Vie</option>
                        <option value="reer" {{ request('insurance_type') === 'reer' ? 'selected' : '' }}>REER</option>
                        <option value="celi" {{ request('insurance_type') === 'celi' ? 'selected' : '' }}>CELI</option>
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                    <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                    <input type="date" name="date_to" id="date_to" value="{{ request('date_to') }}" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Filtrer
                    </button>
                </div>
            </form>

            <!-- Search -->
            <div class="mt-4">
                <form method="GET" action="{{ route('admin.leads') }}">
                    @foreach(request()->except('search') as $key => $value)
                        <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                    @endforeach
                    <div class="flex">
                        <input type="text" name="search" placeholder="Rechercher par nom, email ou téléphone..." 
                               value="{{ request('search') }}"
                               class="flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <button type="submit" class="bg-gray-600 text-white px-6 py-2 rounded-r-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="mb-6">
            <p class="text-gray-600">
                <strong>{{ $leads->total() }}</strong> lead(s) trouvé(s)
                @if(request()->hasAny(['status', 'insurance_type', 'date_from', 'date_to', 'search']))
                    - <a href="{{ route('admin.leads') }}" class="text-blue-600 hover:text-blue-700">Effacer les filtres</a>
                @endif
            </p>
        </div>

        <!-- Leads Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            @if($leads->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type d'assurance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant estimé</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($leads as $lead)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $lead->full_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $lead->email }}</div>
                                        <div class="text-sm text-gray-500">{{ $lead->phone }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($lead->insurance_type === 'visa_visiteur') bg-blue-100 text-blue-800
                                        @elseif($lead->insurance_type === 'assurance_vie') bg-red-100 text-red-800
                                        @elseif($lead->insurance_type === 'reer') bg-green-100 text-green-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst(str_replace('_', ' ', $lead->insurance_type)) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($lead->estimated_amount, 2, ',', ' ') }} $
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($lead->status === 'new') bg-blue-100 text-blue-800
                                        @elseif($lead->status === 'contacted') bg-yellow-100 text-yellow-800
                                        @elseif($lead->status === 'converted') bg-green-100 text-green-800
                                        @else bg-red-100 text-red-800 @endif">
                                        @switch($lead->status)
                                            @case('new') Nouveau @break
                                            @case('contacted') Contacté @break
                                            @case('converted') Converti @break
                                            @case('lost') Perdu @break
                                        @endswitch
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $lead->created_at->format('d/m/Y H:i') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="openUpdateModal({{ $lead->id }}, '{{ $lead->status }}', '{{ addslashes($lead->notes ?? '') }}')" 
                                                class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="mailto:{{ $lead->email }}" class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="tel:{{ $lead->phone }}" class="text-purple-600 hover:text-purple-900">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $leads->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun lead trouvé</h3>
                    <p class="text-gray-500">Aucun lead ne correspond à vos critères de recherche.</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Update Modal -->
<div id="updateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form id="updateForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Mettre à jour le lead</h3>
                    
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
                        <select name="status" id="modalStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="new">Nouveau</option>
                            <option value="contacted">Contacté</option>
                            <option value="converted">Converti</option>
                            <option value="lost">Perdu</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea name="notes" id="modalNotes" rows="3" 
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Ajouter des notes..."></textarea>
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button type="button" onclick="closeUpdateModal()" 
                            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Mettre à jour
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openUpdateModal(leadId, status, notes) {
    document.getElementById('updateForm').action = `/admin/leads/${leadId}`;
    document.getElementById('modalStatus').value = status;
    document.getElementById('modalNotes').value = notes;
    document.getElementById('updateModal').classList.remove('hidden');
}

function closeUpdateModal() {
    document.getElementById('updateModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('updateModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeUpdateModal();
    }
});
</script>
@endpush
@endsection
