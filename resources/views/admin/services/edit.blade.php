@extends('layouts.main')

@section('title', 'Modifier Service - Admin')

@section('content')
<div class="py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Modifier le Service</h1>
                    <p class="text-gray-600 mt-2">{{ $service->name }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('services') }}#{{ $service->slug }}" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        Voir sur le site
                    </a>
                    <a href="{{ route('admin.services.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Retour à la liste
                    </a>
                </div>
            </div>
        </div>

        <form action="{{ route('admin.services.update', $service) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Informations de base -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                    Informations de base
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Nom du service *</label>
                        <input type="text" name="name" id="name" value="{{ old('name', $service->name) }}" required
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
                        <select name="category" id="category" required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Sélectionner une catégorie</option>
                            <option value="assurance" {{ old('category', $service->category) == 'assurance' ? 'selected' : '' }}>Assurance</option>
                            <option value="epargne" {{ old('category', $service->category) == 'epargne' ? 'selected' : '' }}>Épargne et Retraite</option>
                        </select>
                        @error('category')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="is_active" class="block text-sm font-medium text-gray-700 mb-2">Statut *</label>
                        <div class="flex items-center space-x-4 mt-3">
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="1" {{ old('is_active', $service->is_active) ? 'checked' : '' }}
                                       class="text-green-600 focus:ring-green-500">
                                <span class="ml-2 text-sm text-gray-700 flex items-center">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                    Actif
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="0" {{ !old('is_active', $service->is_active) ? 'checked' : '' }}
                                       class="text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700 flex items-center">
                                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                    Inactif
                                </span>
                            </label>
                        </div>
                        @error('is_active')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icône (FontAwesome)</label>
                        <div class="flex items-center space-x-2">
                            @if($service->icon)
                                <i class="{{ $service->icon }} text-blue-600 text-xl"></i>
                            @endif
                            <input type="text" name="icon" id="icon" value="{{ old('icon', $service->icon) }}" placeholder="fas fa-shield-alt"
                                   class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Ex: fas fa-shield-alt, fas fa-heart, fas fa-plane</p>
                        @error('icon')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description courte *</label>
                        <textarea name="description" id="description" rows="2" required
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ old('description', $service->description) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Description affichée sur la page d'accueil et dans les listes</p>
                        @error('description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="detailed_description" class="block text-sm font-medium text-gray-700 mb-2">Description détaillée</label>
                        <textarea name="detailed_description" id="detailed_description" rows="4"
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ old('detailed_description', $service->detailed_description) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Description complète affichée sur la page du service</p>
                        @error('detailed_description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Fonctionnalités -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-list mr-2 text-green-600"></i>
                    Fonctionnalités et avantages
                </h3>

                <div id="features-container">
                    @if($service->features && count($service->features) > 0)
                        @foreach($service->features as $feature)
                        <div class="feature-item flex items-center space-x-2 mb-2">
                            <input type="text" name="features[]" value="{{ $feature }}"
                                   class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        @endforeach
                    @else
                        <div class="feature-item flex items-center space-x-2 mb-2">
                            <input type="text" name="features[]" placeholder="Ex: Couverture médicale d'urgence"
                                   class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    @endif
                </div>

                <button type="button" onclick="addFeature()" class="mt-2 text-blue-600 hover:text-blue-800">
                    <i class="fas fa-plus mr-1"></i>
                    Ajouter une fonctionnalité
                </button>
            </div>

            <!-- Tarification -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-dollar-sign mr-2 text-purple-600"></i>
                    Tarification
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="base_price" class="block text-sm font-medium text-gray-700 mb-2">Prix de base</label>
                        <input type="number" name="base_price" id="base_price" value="{{ old('base_price', $service->base_price) }}" step="0.01" min="0"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Prix de base utilisé pour les calculs de simulation</p>
                        @error('base_price')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Ordre d'affichage</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $service->sort_order) }}" min="0"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Plus le nombre est petit, plus le service apparaît en premier</p>
                        @error('sort_order')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- SEO -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-search mr-2 text-orange-600"></i>
                    Référencement (SEO)
                </h3>

                <div class="space-y-4">
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Titre méta</label>
                        <input type="text" name="meta_title" id="meta_title" value="{{ old('meta_title', $service->meta_title) }}" maxlength="60"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Titre affiché dans les résultats de recherche (max 60 caractères)</p>
                        @error('meta_title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Description méta</label>
                        <textarea name="meta_description" id="meta_description" rows="2" maxlength="160"
                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ old('meta_description', $service->meta_description) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Description affichée dans les résultats de recherche (max 160 caractères)</p>
                        @error('meta_description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Informations du service -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info mr-2 text-gray-600"></i>
                    Informations du service
                </h3>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Slug :</strong> {{ $service->slug }}</p>
                        <p><strong>URL :</strong> <a href="{{ route('services') }}#{{ $service->slug }}" target="_blank" class="text-blue-600 hover:text-blue-800">{{ route('services') }}#{{ $service->slug }}</a></p>
                        <p><strong>Créé le :</strong> {{ $service->created_at->format('d/m/Y à H:i') }}</p>
                        <p><strong>Modifié le :</strong> {{ $service->updated_at->format('d/m/Y à H:i') }}</p>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('admin.services.index') }}" class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors">
                    Annuler
                </a>
                <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Mettre à jour
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function addFeature() {
    const container = document.getElementById('features-container');
    const newFeature = document.createElement('div');
    newFeature.className = 'feature-item flex items-center space-x-2 mb-2';
    newFeature.innerHTML = `
        <input type="text" name="features[]" placeholder="Ex: Couverture médicale d'urgence"
               class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newFeature);
}

function removeFeature(button) {
    const container = document.getElementById('features-container');
    if (container.children.length > 1) {
        button.closest('.feature-item').remove();
    }
}
</script>
@endpush
@endsection
