@extends('layouts.main')

@section('title', 'Gestion des Contrats - Admin')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Gestion des Contrats</h1>
                    <p class="text-gray-600 mt-2">Surveillez les échéances et gérez vos contrats</p>
                </div>
                <a href="{{ route('admin.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Retour au Dashboard
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <form method="GET" action="{{ route('admin.contracts') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les statuts</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Actif</option>
                        <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expiré</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Annulé</option>
                        <option value="renewed" {{ request('status') === 'renewed' ? 'selected' : '' }}>Renouvelé</option>
                    </select>
                </div>

                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de contrat</label>
                    <select name="type" id="type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Tous les types</option>
                        <option value="visa_visiteur" {{ request('type') === 'visa_visiteur' ? 'selected' : '' }}>Visa Visiteur</option>
                        <option value="assurance_vie" {{ request('type') === 'assurance_vie' ? 'selected' : '' }}>Assurance Vie</option>
                        <option value="reer" {{ request('type') === 'reer' ? 'selected' : '' }}>REER</option>
                        <option value="celi" {{ request('type') === 'celi' ? 'selected' : '' }}>CELI</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Filtres rapides</label>
                    <div class="flex items-center">
                        <input type="checkbox" name="expiring_soon" id="expiring_soon" value="1" 
                               {{ request('expiring_soon') ? 'checked' : '' }}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="expiring_soon" class="ml-2 text-sm text-gray-700">Expirant bientôt (30 jours)</label>
                    </div>
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Filtrer
                    </button>
                </div>
            </form>
        </div>

        <!-- Results Summary -->
        <div class="mb-6">
            <p class="text-gray-600">
                <strong>{{ $contracts->total() }}</strong> contrat(s) trouvé(s)
                @if(request()->hasAny(['status', 'type', 'expiring_soon']))
                    - <a href="{{ route('admin.contracts') }}" class="text-blue-600 hover:text-blue-700">Effacer les filtres</a>
                @endif
            </p>
        </div>

        <!-- Contracts Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            @if($contracts->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contrat</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Échéance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($contracts as $contract)
                            <tr class="hover:bg-gray-50 {{ $contract->isExpiringIn(30) ? 'bg-red-50' : '' }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $contract->client_name }}</div>
                                        <div class="text-sm text-gray-500">{{ $contract->client_email }}</div>
                                        <div class="text-sm text-gray-500">{{ $contract->client_phone }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $contract->contract_number }}</div>
                                    <div class="text-sm text-gray-500">Début: {{ $contract->start_date->format('d/m/Y') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($contract->type === 'visa_visiteur') bg-blue-100 text-blue-800
                                        @elseif($contract->type === 'assurance_vie') bg-red-100 text-red-800
                                        @elseif($contract->type === 'reer') bg-green-100 text-green-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst(str_replace('_', ' ', $contract->type)) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ number_format($contract->amount, 2, ',', ' ') }} $
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $contract->end_date->format('d/m/Y') }}</div>
                                    @if($contract->isExpiringIn(30))
                                        <div class="text-xs text-red-600 font-medium">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            Expire dans {{ $contract->end_date->diffInDays(now()) }} jour(s)
                                        </div>
                                    @else
                                        <div class="text-xs text-gray-500">
                                            Dans {{ $contract->end_date->diffInDays(now()) }} jour(s)
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($contract->status === 'active') bg-green-100 text-green-800
                                        @elseif($contract->status === 'expired') bg-red-100 text-red-800
                                        @elseif($contract->status === 'cancelled') bg-gray-100 text-gray-800
                                        @else bg-blue-100 text-blue-800 @endif">
                                        @switch($contract->status)
                                            @case('active') Actif @break
                                            @case('expired') Expiré @break
                                            @case('cancelled') Annulé @break
                                            @case('renewed') Renouvelé @break
                                        @endswitch
                                    </span>
                                    @if($contract->reminder_sent)
                                        <div class="text-xs text-blue-600 mt-1">
                                            <i class="fas fa-bell mr-1"></i>
                                            Rappel envoyé
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="mailto:{{ $contract->client_email }}" class="text-green-600 hover:text-green-900" title="Envoyer un email">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                        <a href="tel:{{ $contract->client_phone }}" class="text-purple-600 hover:text-purple-900" title="Appeler">
                                            <i class="fas fa-phone"></i>
                                        </a>
                                        @if($contract->isExpiringIn(30) && !$contract->reminder_sent)
                                            <button onclick="sendReminder({{ $contract->id }})" class="text-orange-600 hover:text-orange-900" title="Envoyer rappel">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $contracts->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-file-contract text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun contrat trouvé</h3>
                    <p class="text-gray-500">Aucun contrat ne correspond à vos critères de recherche.</p>
                </div>
            @endif
        </div>

        <!-- Quick Actions -->
        @if($contracts->where('status', 'active')->where('end_date', '<=', now()->addDays(30))->count() > 0)
        <div class="mt-6 bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-orange-900 mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Actions recommandées
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="sendAllReminders()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-bell mr-2"></i>
                    Envoyer tous les rappels d'échéance
                </button>
                <a href="{{ route('admin.contracts', ['expiring_soon' => 1]) }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center">
                    <i class="fas fa-filter mr-2"></i>
                    Voir uniquement les contrats expirant
                </a>
            </div>
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
function sendReminder(contractId) {
    if (confirm('Êtes-vous sûr de vouloir envoyer un rappel pour ce contrat ?')) {
        // Ici vous pourriez faire un appel AJAX pour envoyer le rappel
        fetch(`/admin/contracts/${contractId}/send-reminder`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Rappel envoyé avec succès !');
                location.reload();
            } else {
                alert('Erreur lors de l\'envoi du rappel.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de l\'envoi du rappel.');
        });
    }
}

function sendAllReminders() {
    if (confirm('Êtes-vous sûr de vouloir envoyer des rappels pour tous les contrats expirant bientôt ?')) {
        // Ici vous pourriez faire un appel AJAX pour envoyer tous les rappels
        fetch('/admin/contracts/send-all-reminders', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`${data.count} rappel(s) envoyé(s) avec succès !`);
                location.reload();
            } else {
                alert('Erreur lors de l\'envoi des rappels.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de l\'envoi des rappels.');
        });
    }
}
</script>
@endpush
@endsection
