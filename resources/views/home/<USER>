@extends('layouts.main')

@section('title', 'Contact - Assurance iA Montréal')
@section('description', 'Contactez nos experts en assurance à Montréal. Réponse rapide garantie pour tous vos besoins d\'assurance.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-indigo-600 to-purple-800 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">Contactez-nous</h1>
        <p class="text-xl text-indigo-100">
            Nos experts sont là pour répondre à toutes vos questions
        </p>
    </div>
</section>

<!-- Contact Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {{ session('error') }}
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div class="bg-white rounded-2xl shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">
                    <i class="fas fa-envelope mr-2 text-blue-600"></i>
                    Envoyez-nous un message
                </h2>

                <form action="{{ route('contact.submit') }}" method="POST">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Nom complet *
                            </label>
                            <input type="text" id="name" name="name" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="Votre nom" value="{{ old('name') }}" required>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                Téléphone *
                            </label>
                            <input type="tel" id="phone" name="phone" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                   placeholder="(*************" value="{{ old('phone') }}" required>
                            @error('phone')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-6">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Adresse email *
                        </label>
                        <input type="email" id="email" name="email" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="<EMAIL>" value="{{ old('email') }}" required>
                        @error('email')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6">
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                            Sujet *
                        </label>
                        <select id="subject" name="subject" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                            <option value="">Sélectionnez un sujet</option>
                            <option value="visa_visiteur" {{ old('subject') == 'visa_visiteur' ? 'selected' : '' }}>Assurance Visa Visiteur</option>
                            <option value="assurance_vie" {{ old('subject') == 'assurance_vie' ? 'selected' : '' }}>Assurance Vie</option>
                            <option value="reer" {{ old('subject') == 'reer' ? 'selected' : '' }}>REER</option>
                            <option value="celi" {{ old('subject') == 'celi' ? 'selected' : '' }}>CELI</option>
                            <option value="renouvellement" {{ old('subject') == 'renouvellement' ? 'selected' : '' }}>Renouvellement</option>
                            <option value="reclamation" {{ old('subject') == 'reclamation' ? 'selected' : '' }}>Réclamation</option>
                            <option value="autre" {{ old('subject') == 'autre' ? 'selected' : '' }}>Autre</option>
                        </select>
                        @error('subject')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="mb-6">
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                            Message *
                        </label>
                        <textarea id="message" name="message" rows="6" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                                  placeholder="Décrivez votre demande ou vos questions..." required>{{ old('message') }}</textarea>
                        @error('message')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="text-center">
                        <button type="submit" class="w-full bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Envoyer le message
                        </button>
                    </div>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="space-y-8">
                <!-- Contact Details -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>
                        Nos coordonnées
                    </h2>

                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-phone text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Téléphone</h3>
                                <p class="text-gray-600">(*************</p>
                                <p class="text-sm text-gray-500">Lun-Ven: 9h00-17h00</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-envelope text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Email</h3>
                                <p class="text-gray-600"><EMAIL></p>
                                <p class="text-sm text-gray-500">Réponse sous 2h ouvrables</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-map-marker-alt text-purple-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Adresse</h3>
                                <p class="text-gray-600">Montréal, Québec</p>
                                <p class="text-sm text-gray-500">Rendez-vous sur demande</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-yellow-100 rounded-full w-12 h-12 flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="fas fa-clock text-yellow-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Heures d'ouverture</h3>
                                <div class="text-gray-600 text-sm">
                                    <p>Lundi - Vendredi: 9h00 - 17h00</p>
                                    <p>Samedi: Sur rendez-vous</p>
                                    <p>Dimanche: Fermé</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact -->
                <div class="bg-red-50 border border-red-200 rounded-2xl p-8">
                    <h3 class="text-xl font-bold text-red-900 mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Urgence 24/7
                    </h3>
                    <p class="text-red-800 mb-4">
                        Pour les urgences médicales à l'étranger ou les réclamations urgentes
                    </p>
                    <a href="tel:+15141234567" class="bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors inline-block">
                        <i class="fas fa-phone mr-2"></i>
                        Ligne d'urgence
                    </a>
                </div>

                <!-- Quick Actions -->
                <div class="bg-blue-50 border border-blue-200 rounded-2xl p-8">
                    <h3 class="text-xl font-bold text-blue-900 mb-4">
                        <i class="fas fa-rocket mr-2"></i>
                        Actions rapides
                    </h3>
                    <div class="space-y-3">
                        <a href="{{ route('simulation.index') }}" class="block bg-white border border-blue-200 rounded-lg p-3 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-calculator text-blue-600 mr-3"></i>
                                <span class="font-medium">Simulation gratuite</span>
                            </div>
                        </a>
                        <a href="{{ route('appointment.create') }}" class="block bg-white border border-blue-200 rounded-lg p-3 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-blue-600 mr-3"></i>
                                <span class="font-medium">Prendre rendez-vous</span>
                            </div>
                        </a>
                        <a href="{{ route('services') }}" class="block bg-white border border-blue-200 rounded-lg p-3 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-blue-600 mr-3"></i>
                                <span class="font-medium">Nos services</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Questions fréquentes</h2>
            <p class="text-lg text-gray-600">Trouvez rapidement les réponses à vos questions</p>
        </div>

        <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Combien de temps pour recevoir une réponse ?</h3>
                <p class="text-gray-600">Nous nous engageons à répondre à tous les messages dans les 2 heures ouvrables. Pour les urgences, appelez directement notre ligne téléphonique.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Puis-je obtenir un devis par téléphone ?</h3>
                <p class="text-gray-600">Absolument ! Nos conseillers peuvent vous fournir un devis personnalisé par téléphone. Cependant, notre simulateur en ligne vous donnera une estimation immédiate.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Offrez-vous des consultations le weekend ?</h3>
                <p class="text-gray-600">Nous offrons des consultations sur rendez-vous le samedi pour s'adapter à votre emploi du temps. Contactez-nous pour planifier un rendez-vous.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Vos services sont-ils disponibles en français et en anglais ?</h3>
                <p class="text-gray-600">Oui, tous nos services sont disponibles en français et en anglais. Nos conseillers sont bilingues pour mieux vous servir.</p>
            </div>
        </div>
    </div>
</section>
@endsection
