@extends('layouts.main')

@section('title', 'Nos Services d\'Assurance - Assurance iA Montréal')
@section('description', 'Découvrez nos services d\'assurance : visa visiteur, assurance vie, REER et CELI. Solutions personnalisées avec iA Groupe financier.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-indigo-600 to-purple-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Nos Services d'Assurance</h1>
            <p class="text-xl text-indigo-100 max-w-2xl mx-auto">
                Des solutions complètes et personnalisées pour protéger ce qui compte le plus pour vous
            </p>
        </div>
    </div>
</section>

<!-- Services par catégories -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Section Assurances -->
        <div class="mb-16">
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                    <i class="fas fa-shield-alt text-indigo-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Nos Assurances</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Protégez-vous et votre famille avec nos solutions d'assurance adaptées à vos besoins
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                @foreach($assuranceServices as $service)
                    <div id="{{ $service->slug }}" class="bg-white rounded-lg border border-gray-200 p-8 hover:shadow-md transition-shadow duration-200">
                        <!-- Header simple -->
                        <div class="flex items-start mb-6">
                            <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                <i class="{{ $service->icon }} text-indigo-600 text-2xl"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $service->name }}</h3>
                                <p class="text-gray-600">{{ $service->description }}</p>
                                @if($service->base_price)
                                    <div class="text-sm text-gray-500 mt-2">
                                        À partir de <span class="font-semibold text-indigo-600">{{ number_format($service->base_price, 0, ',', ' ') }}$</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Avantages -->
                        @if($service->features && count($service->features) > 0)
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Avantages inclus</h4>
                            <ul class="space-y-2">
                                @foreach(array_slice($service->features, 0, 6) as $feature)
                                <li class="flex items-start text-sm text-gray-600">
                                    <i class="fas fa-check text-green-500 mr-3 mt-0.5 flex-shrink-0"></i>
                                    <span>{{ $feature }}</span>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        @endif

                        <!-- Actions -->
                        <div class="flex gap-4 mt-6">
                            <a href="{{ route('simulation.index') }}"
                               class="bg-indigo-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                                Obtenir un devis
                            </a>
                            <a href="{{ route('appointment.create') }}"
                               class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                                Rendez-vous
                            </a>
                        </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Section Épargne et Retraite -->
        <div class="mb-16">
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                    <i class="fas fa-piggy-bank text-indigo-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Épargne et Retraite</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Préparez votre avenir financier avec nos solutions d'épargne et de placement
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                @foreach($epargneServices as $service)
                    <div id="{{ $service->slug }}" class="group bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl hover:-translate-y-1 transition-all duration-300">
                        <!-- Header avec gradient -->
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 border-b border-gray-100">
                            <div class="flex items-start justify-between mb-4">
                                <div class="bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl w-16 h-16 flex items-center justify-center shadow-lg">
                                    <i class="{{ $service->icon }} text-white text-2xl"></i>
                                </div>
                                @if($service->base_price)
                                    <div class="text-right">
                                        <span class="text-xs text-gray-500">À partir de</span>
                                        <div class="text-xl font-bold text-green-600">{{ number_format($service->base_price, 0, ',', ' ') }}$</div>
                                    </div>
                                @endif
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $service->name }}</h3>
                            <p class="text-gray-600 text-sm leading-relaxed">{{ $service->description }}</p>
                        </div>

                        <!-- Contenu -->
                        <div class="p-6">
                            @if($service->features && count($service->features) > 0)
                            <div class="mb-6">
                                <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                    <i class="fas fa-star text-yellow-500 mr-2"></i>
                                    Avantages inclus
                                </h4>
                                <ul class="space-y-2 max-h-48 overflow-y-auto">
                                    @foreach($service->features as $feature)
                                    <li class="flex items-start text-sm">
                                        <i class="fas fa-check text-green-500 mr-2 mt-0.5 flex-shrink-0"></i>
                                        <span class="text-gray-700">{{ $feature }}</span>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                            @endif

                            @if($service->detailed_description)
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 mb-6 border border-green-100">
                                <h4 class="text-sm font-semibold text-green-900 mb-2 flex items-center">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    Description détaillée
                                </h4>
                                <div class="text-sm text-green-800 leading-relaxed">
                                    {!! nl2br(e(Str::limit($service->detailed_description, 300))) !!}
                                </div>
                            </div>
                            @endif

                            <!-- Actions -->
                            <div class="flex flex-col sm:flex-row gap-3 mt-auto">
                                <a href="{{ route('simulation.index') }}" class="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-200 text-center shadow-md hover:shadow-lg">
                                    <i class="fas fa-calculator mr-2"></i>
                                    Obtenir un devis
                                </a>
                                <a href="{{ route('appointment.create') }}" class="flex-1 border-2 border-green-200 text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 hover:border-green-300 transition-all duration-200 text-center">
                                    <i class="fas fa-calendar-alt mr-2"></i>
                                    Rendez-vous
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Comment ça marche ?</h2>
            <p class="text-lg text-gray-600">Un processus simple et transparent en 4 étapes</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">1</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Simulation</h3>
                <p class="text-gray-600">Obtenez votre devis personnalisé en ligne en quelques minutes</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">2</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Consultation</h3>
                <p class="text-gray-600">Rencontrez un de nos experts pour affiner votre solution</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">3</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Souscription</h3>
                <p class="text-gray-600">Finalisez votre contrat avec notre accompagnement personnalisé</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">4</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Suivi</h3>
                <p class="text-gray-600">Bénéficiez de notre suivi continu et de nos rappels automatiques</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Questions fréquentes</h2>
            <p class="text-lg text-gray-600">Trouvez rapidement les réponses à vos questions</p>
        </div>

        <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Pourquoi choisir iA Groupe financier ?</h3>
                <p class="text-gray-600">iA Groupe financier est l'un des leaders de l'assurance au Canada avec plus de 125 ans d'expérience. Nous offrons la stabilité, l'expertise et la confiance que vous méritez.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Combien coûte une assurance visa visiteur ?</h3>
                <p class="text-gray-600">Le coût varie selon l'âge, la durée du séjour et le montant de couverture. Utilisez notre simulateur pour obtenir un devis personnalisé instantané.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Puis-je modifier mon contrat après souscription ?</h3>
                <p class="text-gray-600">Oui, la plupart de nos produits offrent une flexibilité permettant d'ajuster votre couverture selon l'évolution de vos besoins.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Comment sont gérés les rappels d'échéance ?</h3>
                <p class="text-gray-600">Notre système automatisé vous envoie des rappels par email et SMS avant l'échéance de votre contrat pour vous permettre de le renouveler en temps voulu.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Prêt à commencer ?</h2>
        <p class="text-xl mb-8 text-indigo-100">
            Obtenez votre devis personnalisé ou prenez rendez-vous avec l'un de nos experts
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('simulation.index') }}" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-indigo-50 transition-colors">
                <i class="fas fa-calculator mr-2"></i>
                Simulation gratuite
            </a>
            <a href="{{ route('appointment.create') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors">
                <i class="fas fa-calendar-alt mr-2"></i>
                Prendre rendez-vous
            </a>
        </div>
    </div>
</section>
@endsection
