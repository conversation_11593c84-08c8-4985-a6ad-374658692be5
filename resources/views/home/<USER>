@extends('layouts.main')

@section('title', 'Nos Services d\'Assurance - Assurance iA Montréal')
@section('description', 'Découvrez nos services d\'assurance : visa visiteur, assurance vie, REER et CELI. Solutions personnalisées avec iA Groupe financier.')

@section('content')
<style>
.service-card { display: none; }
.service-card.active { display: block; }
.service-card:target { display: block !important; }
</style>
<!-- Hero Section -->
<section class="bg-gradient-to-r from-indigo-600 to-purple-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Nos Services d'Assurance</h1>
            <p class="text-xl text-indigo-100 max-w-2xl mx-auto">
                Des solutions complètes et personnalisées pour protéger ce qui compte le plus pour vous
            </p>
        </div>
    </div>
</section>

<!-- Services par catégories -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Section Assurances -->
        <div class="mb-16 section-assurances">
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                    <i class="fas fa-shield-alt text-indigo-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Nos Assurances</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Protégez-vous et votre famille avec nos solutions d'assurance adaptées à vos besoins
                </p>
            </div>

            <div class="max-w-4xl mx-auto">
                <div class="space-y-8">
                    @foreach($assuranceServices as $service)
                        <div id="{{ $service->slug }}" class="service-card bg-white rounded-lg border border-gray-200 p-8">
                            <div class="flex items-start mb-6">
                                <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mr-6 flex-shrink-0">
                                    <i class="{{ $service->icon }} text-indigo-600 text-2xl"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-2xl font-bold text-gray-900 mb-3">{{ $service->name }}</h3>
                                    <p class="text-gray-600 text-lg leading-relaxed">{{ $service->description }}</p>
                                </div>
                            </div>

                            @if($service->features && count($service->features) > 0)
                            <div class="mb-8">
                                <h4 class="text-lg font-semibold text-gray-900 mb-4">Ce qui est inclus</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    @foreach($service->features as $feature)
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">{{ $feature }}</span>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <div class="flex gap-4">
                                <a href="{{ route('simulation.index') }}"
                                   class="bg-indigo-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors">
                                    Obtenir un devis gratuit
                                </a>
                                <a href="{{ route('appointment.create') }}"
                                   class="border-2 border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                                    Prendre rendez-vous
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Section Épargne et Retraite -->
        <div class="mb-16 section-epargne">
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                    <i class="fas fa-piggy-bank text-indigo-600 text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Épargne et Retraite</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Préparez votre avenir financier avec nos solutions d'épargne et de placement
                </p>
            </div>

            <div class="space-y-8">
                @foreach($epargneServices as $service)
                    <div id="{{ $service->slug }}" class="service-card bg-white rounded-lg border border-gray-200 p-8">
                        <div class="flex items-start mb-6">
                            <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mr-6 flex-shrink-0">
                                <i class="{{ $service->icon }} text-green-600 text-2xl"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-2xl font-bold text-gray-900 mb-3">{{ $service->name }}</h3>
                                <p class="text-gray-600 text-lg leading-relaxed">{{ $service->description }}</p>
                            </div>
                        </div>

                        @if($service->features && count($service->features) > 0)
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Avantages inclus</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @foreach($service->features as $feature)
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $feature }}</span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <div class="flex gap-4">
                            <a href="{{ route('simulation.index') }}"
                               class="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                Obtenir un devis gratuit
                            </a>
                            <a href="{{ route('appointment.create') }}"
                               class="border-2 border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                                Prendre rendez-vous
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</section>

<!-- Process Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Comment ça marche ?</h2>
            <p class="text-lg text-gray-600">Un processus simple et transparent en 4 étapes</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">1</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Simulation</h3>
                <p class="text-gray-600">Obtenez votre devis personnalisé en ligne en quelques minutes</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">2</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Consultation</h3>
                <p class="text-gray-600">Rencontrez un de nos experts pour affiner votre solution</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">3</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Souscription</h3>
                <p class="text-gray-600">Finalisez votre contrat avec notre accompagnement personnalisé</p>
            </div>

            <div class="text-center">
                <div class="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-indigo-600">4</span>
                </div>
                <h3 class="text-lg font-semibold mb-2">Suivi</h3>
                <p class="text-gray-600">Bénéficiez de notre suivi continu et de nos rappels automatiques</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Questions fréquentes</h2>
            <p class="text-lg text-gray-600">Trouvez rapidement les réponses à vos questions</p>
        </div>

        <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Pourquoi choisir iA Groupe financier ?</h3>
                <p class="text-gray-600">iA Groupe financier est l'un des leaders de l'assurance au Canada avec plus de 125 ans d'expérience. Nous offrons la stabilité, l'expertise et la confiance que vous méritez.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Combien coûte une assurance visa visiteur ?</h3>
                <p class="text-gray-600">Le coût varie selon l'âge, la durée du séjour et le montant de couverture. Utilisez notre simulateur pour obtenir un devis personnalisé instantané.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Puis-je modifier mon contrat après souscription ?</h3>
                <p class="text-gray-600">Oui, la plupart de nos produits offrent une flexibilité permettant d'ajuster votre couverture selon l'évolution de vos besoins.</p>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-2">Comment sont gérés les rappels d'échéance ?</h3>
                <p class="text-gray-600">Notre système automatisé vous envoie des rappels par email et SMS avant l'échéance de votre contrat pour vous permettre de le renouveler en temps voulu.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Prêt à commencer ?</h2>
        <p class="text-xl mb-8 text-indigo-100">
            Obtenez votre devis personnalisé ou prenez rendez-vous avec l'un de nos experts
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('simulation.index') }}" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-indigo-50 transition-colors">
                <i class="fas fa-calculator mr-2"></i>
                Simulation gratuite
            </a>
            <a href="{{ route('appointment.create') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors">
                <i class="fas fa-calendar-alt mr-2"></i>
                Prendre rendez-vous
            </a>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Liste des services d'assurance
    const assuranceServices = @json($assuranceServices->pluck('slug'));
    // Liste des services d'épargne
    const epargneServices = @json($epargneServices->pluck('slug'));

    // Fonction pour afficher un service spécifique
    function showService(serviceId) {
        // Masquer tous les services
        document.querySelectorAll('.service-card').forEach(card => {
            card.style.display = 'none';
        });

        // Déterminer la catégorie du service
        const isAssurance = assuranceServices.includes(serviceId);
        const isEpargne = epargneServices.includes(serviceId);

        // Masquer/afficher les sections selon la catégorie
        const sectionAssurances = document.querySelector('.section-assurances');
        const sectionEpargne = document.querySelector('.section-epargne');

        if (isAssurance) {
            // Afficher seulement la section assurances
            sectionAssurances.style.display = 'block';
            sectionEpargne.style.display = 'none';
        } else if (isEpargne) {
            // Afficher seulement la section épargne
            sectionAssurances.style.display = 'none';
            sectionEpargne.style.display = 'block';
        } else {
            // Afficher toutes les sections
            sectionAssurances.style.display = 'block';
            sectionEpargne.style.display = 'block';
        }

        // Afficher le service demandé
        const targetService = document.getElementById(serviceId);
        if (targetService) {
            targetService.style.display = 'block';
            targetService.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // Vérifier s'il y a une ancre dans l'URL
    const hash = window.location.hash.substring(1);
    if (hash) {
        showService(hash);
    } else {
        // Si pas d'ancre, afficher tous les services et toutes les sections
        document.querySelectorAll('.service-card').forEach(card => {
            card.style.display = 'block';
        });
        document.querySelector('.section-assurances').style.display = 'block';
        document.querySelector('.section-epargne').style.display = 'block';
    }

    // Écouter les changements d'ancre
    window.addEventListener('hashchange', function() {
        const newHash = window.location.hash.substring(1);
        if (newHash) {
            showService(newHash);
        } else {
            document.querySelectorAll('.service-card').forEach(card => {
                card.style.display = 'block';
            });
            document.querySelector('.section-assurances').style.display = 'block';
            document.querySelector('.section-epargne').style.display = 'block';
        }
    });
});
</script>
@endsection
