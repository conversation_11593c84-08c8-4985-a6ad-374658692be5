@extends('layouts.main')

@section('title', 'Assurance iA Montréal - Votre partenaire assurance de confiance')
@section('description', 'Assurance iA Montréal, spécialiste en assurance visa visiteur, assurance vie, REER et CELI. Obtenez votre devis gratuit en ligne et prenez rendez-vous avec nos experts.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-indigo-600 to-purple-800 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                    {{ setting('hero_title', 'Votre partenaire assurance de confiance à Montréal') }}
                </h1>
                <p class="text-xl mb-8 text-indigo-100">
                    {{ setting('hero_description', 'Spécialisés dans l\'assurance et l\'épargne. Affilié à iA Groupe financier pour votre tranquillité d\'esprit.') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('simulation.index') }}" class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-indigo-50 transition-colors text-center">
                        <i class="fas fa-calculator mr-2"></i>
                        Simulation gratuite
                    </a>
                    <a href="{{ route('appointment.create') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors text-center">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Prendre rendez-vous
                    </a>
                </div>
            </div>
            <div class="hidden lg:block">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                    <div class="grid grid-cols-2 gap-6">
                        @foreach($services->take(4) as $service)
                        <div class="text-center">
                            <div class="bg-white/20 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                                <i class="{{ $service->icon }} text-2xl"></i>
                            </div>
                            <h3 class="font-semibold mb-2">{{ \Illuminate\Support\Str::limit($service->name, 15) }}</h3>
                            <p class="text-sm text-indigo-100">{{ \Illuminate\Support\Str::limit($service->description, 20) }}</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ setting('services_title', 'Nos Services d\'Assurance') }}</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                {{ setting('services_description', 'Des solutions complètes et personnalisées pour protéger ce qui compte le plus pour vous') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($services as $service)
            <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <!-- Icône simple -->
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="{{ $service->icon }} text-indigo-600 text-xl"></i>
                </div>

                <!-- Titre et description -->
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $service->name }}</h3>
                <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                    {{ Str::limit($service->description, 80) }}
                </p>

                <!-- Prix si disponible -->
                @if($service->base_price)
                <div class="text-sm text-gray-500 mb-4">
                    À partir de <span class="font-semibold text-indigo-600">{{ number_format($service->base_price, 0, ',', ' ') }}$</span>
                </div>
                @endif

                <!-- Action simple -->
                <a href="{{ route('home.services') }}#{{ $service->slug }}"
                   class="inline-flex items-center text-indigo-600 text-sm font-medium hover:text-indigo-700 transition-colors">
                    En savoir plus
                    <i class="fas fa-arrow-right ml-2 text-xs"></i>
                </a>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ setting('why_choose_title', 'Pourquoi nous choisir ?') }}</h2>
            <p class="text-lg text-gray-600">{{ setting('why_choose_description', 'L\'expertise et la confiance d\'iA Groupe financier à votre service') }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-amber-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-award text-amber-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">Expertise reconnue</h3>
                <p class="text-gray-600">Plus de 20 ans d'expérience dans le domaine de l'assurance à Montréal.</p>
            </div>

            <div class="text-center">
                <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-handshake text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">Service personnalisé</h3>
                <p class="text-gray-600">Accompagnement sur mesure pour trouver la solution qui vous convient.</p>
            </div>

            <div class="text-center">
                <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold mb-3">Sécurité garantie</h3>
                <p class="text-gray-600">Affilié à iA Groupe financier, l'un des leaders de l'assurance au Canada.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-emerald-600 to-teal-700 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">{{ setting('cta_title', 'Prêt à protéger votre avenir ?') }}</h2>
        <p class="text-xl mb-8 text-emerald-100">
            {{ setting('cta_description', 'Obtenez votre devis personnalisé en quelques minutes ou prenez rendez-vous avec l\'un de nos experts.') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('simulation.index') }}" class="bg-white text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-emerald-50 transition-colors">
                <i class="fas fa-calculator mr-2"></i>
                Simulation gratuite
            </a>
            <a href="{{ route('appointment.create') }}" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-emerald-600 transition-colors">
                <i class="fas fa-calendar-alt mr-2"></i>
                Prendre rendez-vous
            </a>
        </div>
    </div>
</section>
@endsection
