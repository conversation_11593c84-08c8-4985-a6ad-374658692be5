<div>
    <p class="text-red-600 mb-6">
        Une fois votre compte supprimé, toutes ses ressources et données seront définitivement supprimées.
        Avant de supprimer votre compte, veuillez télécharger toutes les données ou informations que vous souhaitez conserver.
    </p>

    <button onclick="openDeleteModal()" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors">
        <i class="fas fa-trash mr-2"></i>
        Supprimer le compte
    </button>
</div>

<!-- Delete Account Modal -->
<div id="deleteAccountModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <form method="post" action="{{ route('profile.destroy') }}">
                @csrf
                @method('delete')
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-red-100 rounded-full p-3 mr-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Confirmer la suppression</h3>
                            <p class="text-sm text-gray-500">Cette action est irréversible</p>
                        </div>
                    </div>

                    <p class="text-gray-700 mb-4">
                        Êtes-vous sûr de vouloir supprimer votre compte ? Toutes vos données seront définitivement supprimées.
                    </p>

                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirmez avec votre mot de passe
                        </label>
                        <input type="password" name="password" id="password" required
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               placeholder="Votre mot de passe">
                        @error('password', 'userDeletion')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button type="button" onclick="closeDeleteModal()"
                            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Supprimer définitivement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openDeleteModal() {
    document.getElementById('deleteAccountModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteAccountModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteAccountModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Show modal if there are validation errors
@if($errors->userDeletion->isNotEmpty())
    openDeleteModal();
@endif
</script>
@endpush
