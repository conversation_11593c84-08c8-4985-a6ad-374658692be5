@extends('layouts.main')

@section('title', 'Rendez-vous Confirmé - Assurance iA Montréal')
@section('description', 'Votre rendez-vous a été confirmé avec succès. Nous avons hâte de vous rencontrer.')

@section('content')
<!-- Success Hero -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="mb-6">
            <i class="fas fa-check-circle text-6xl text-green-200"></i>
        </div>
        <h1 class="text-4xl font-bold mb-4">Rendez-vous Confirmé !</h1>
        <p class="text-xl text-green-100">
            Merci {{ $appointment->client_name }}, votre rendez-vous a été enregistré avec succès
        </p>
    </div>
</section>

<!-- Appointment Details -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Confirmation Card -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Détails de votre rendez-vous</h2>
                <p class="text-gray-600">
                    Nous vous avons envoyé un email de confirmation à {{ $appointment->client_email }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Appointment Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-900">
                        <i class="fas fa-calendar-alt mr-2 text-blue-600"></i>
                        Informations du rendez-vous
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Date :</span>
                            <span class="font-medium">{{ $appointment->appointment_date->format('d/m/Y') }}</span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Heure :</span>
                            <span class="font-medium">{{ $appointment->appointment_date->format('H:i') }}</span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Service :</span>
                            <span class="font-medium">
                                @switch($appointment->service_type)
                                    @case('visa_visiteur')
                                        Assurance Visa Visiteur
                                        @break
                                    @case('assurance_vie')
                                        Assurance Vie
                                        @break
                                    @case('reer')
                                        REER
                                        @break
                                    @case('celi')
                                        CELI
                                        @break
                                    @case('consultation_generale')
                                        Consultation générale
                                        @break
                                    @case('renouvellement')
                                        Renouvellement de contrat
                                        @break
                                    @default
                                        {{ ucfirst(str_replace('_', ' ', $appointment->service_type)) }}
                                @endswitch
                            </span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Statut :</span>
                            <span class="font-medium text-yellow-600">
                                <i class="fas fa-clock mr-1"></i>
                                En attente de confirmation
                            </span>
                        </div>

                        <div class="flex justify-between py-2">
                            <span class="text-gray-600">Durée estimée :</span>
                            <span class="font-medium">30-45 minutes</span>
                        </div>
                    </div>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-900">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        Vos coordonnées
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Nom :</span>
                            <span class="font-medium">{{ $appointment->client_name }}</span>
                        </div>

                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <span class="text-gray-600">Email :</span>
                            <span class="font-medium">{{ $appointment->client_email }}</span>
                        </div>

                        <div class="flex justify-between py-2">
                            <span class="text-gray-600">Téléphone :</span>
                            <span class="font-medium">{{ $appointment->client_phone }}</span>
                        </div>
                    </div>

                    @if($appointment->message)
                    <div class="mt-6">
                        <h4 class="font-semibold text-gray-900 mb-2">Votre message :</h4>
                        <div class="bg-gray-50 rounded-lg p-3 text-sm text-gray-700">
                            {{ $appointment->message }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-blue-50 border border-blue-200 rounded-2xl p-8 mb-8">
            <h3 class="text-xl font-bold text-blue-900 mb-6 text-center">
                <i class="fas fa-route mr-2"></i>
                Prochaines étapes
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-blue-600">1</span>
                    </div>
                    <h4 class="font-semibold mb-2 text-blue-900">Confirmation</h4>
                    <p class="text-blue-800 text-sm">
                        Nous confirmerons votre rendez-vous dans les 2 heures ouvrables
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-green-600">2</span>
                    </div>
                    <h4 class="font-semibold mb-2 text-blue-900">Préparation</h4>
                    <p class="text-blue-800 text-sm">
                        Préparez vos questions et documents pertinents
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-purple-600">3</span>
                    </div>
                    <h4 class="font-semibold mb-2 text-purple-900">Rencontre</h4>
                    <p class="text-purple-800 text-sm">
                        Consultation personnalisée avec notre expert
                    </p>
                </div>
            </div>
        </div>

        <!-- Preparation Tips -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6">
                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                Comment bien préparer votre rendez-vous
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">📋 Documents à apporter</h4>
                    <ul class="space-y-2 text-sm text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            <span>Pièce d'identité avec photo</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            <span>Relevés bancaires récents</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            <span>Contrats d'assurance existants (si applicable)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                            <span>Informations sur vos besoins spécifiques</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">❓ Questions à préparer</h4>
                    <ul class="space-y-2 text-sm text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-question-circle text-blue-500 mr-2 mt-1"></i>
                            <span>Quels sont mes besoins de protection ?</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-question-circle text-blue-500 mr-2 mt-1"></i>
                            <span>Quel budget puis-je allouer ?</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-question-circle text-blue-500 mr-2 mt-1"></i>
                            <span>Quelles sont les options disponibles ?</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-question-circle text-blue-500 mr-2 mt-1"></i>
                            <span>Comment optimiser ma fiscalité ?</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Contact & Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Contact Card -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-phone mr-2 text-green-600"></i>
                    Besoin de nous contacter ?
                </h3>
                <div class="space-y-3 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-phone text-gray-400 mr-3"></i>
                        <span>(*************</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-envelope text-gray-400 mr-3"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock text-gray-400 mr-3"></i>
                        <span>Lun-Ven: 9h00-17h00</span>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="tel:+15141234567" class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-green-700 transition-colors">
                        <i class="fas fa-phone mr-2"></i>
                        Appeler maintenant
                    </a>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-cog mr-2 text-indigo-600"></i>
                    Gérer votre rendez-vous
                </h3>
                <div class="space-y-3">
                    <button onclick="addToCalendar()" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Ajouter au calendrier
                    </button>
                    <a href="{{ route('contact') }}" class="block w-full bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-700 transition-colors text-center">
                        <i class="fas fa-edit mr-2"></i>
                        Modifier le rendez-vous
                    </a>
                </div>
                <p class="text-xs text-gray-500 mt-3">
                    Pour annuler ou reporter, contactez-nous au moins 24h à l'avance
                </p>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
function addToCalendar() {
    const startDate = new Date('{{ $appointment->appointment_date->format('Y-m-d H:i:s') }}');
    const endDate = new Date(startDate.getTime() + (45 * 60 * 1000)); // +45 minutes
    
    const title = 'Rendez-vous Assurance iA Montréal';
    const details = 'Consultation {{ $appointment->service_type }} avec Assurance iA Montréal';
    const location = 'Assurance iA Montréal, Montréal, QC';
    
    // Format pour Google Calendar
    const googleUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${startDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z/${endDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z&details=${encodeURIComponent(details)}&location=${encodeURIComponent(location)}`;
    
    window.open(googleUrl, '_blank');
}
</script>
@endpush
@endsection
