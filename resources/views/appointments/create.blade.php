@extends('layouts.main')

@section('title', '<PERSON><PERSON><PERSON>vous - Assurance iA Montréal')
@section('description', 'Prenez rendez-vous avec nos experts en assurance. Consultation gratuite et personnalisée.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-indigo-600 to-purple-700 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">Prendre <PERSON>-vous</h1>
        <p class="text-xl text-indigo-100">
            Rencontrez nos experts pour une consultation personnalisée et gratuite
        </p>
    </div>
</section>

<!-- Appointment Form -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {{ session('error') }}
            </div>
        @endif

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-calendar-alt mr-2 text-indigo-600"></i>
                        Réserver votre créneau
                    </h2>

                    <form action="{{ route('appointment.store') }}" method="POST" id="appointment-form">
                        @csrf
                        
                        <!-- Informations personnelles -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Vos coordonnées</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="client_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        Nom complet *
                                    </label>
                                    <input type="text" id="client_name" name="client_name"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="Prénom Nom" value="{{ old('client_name') }}" required>
                                    @error('client_name')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="client_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        Téléphone *
                                    </label>
                                    <input type="tel" id="client_phone" name="client_phone"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="(*************" value="{{ old('client_phone') }}" required>
                                    @error('client_phone')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-6">
                                <label for="client_email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Adresse email *
                                </label>
                                <input type="email" id="client_email" name="client_email"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="<EMAIL>" value="{{ old('client_email') }}" required>
                                @error('client_email')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Service souhaité -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Service souhaité</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($services as $key => $label)
                                <label class="relative">
                                    <input type="radio" name="service_type" value="{{ $key }}" class="sr-only peer" required>
                                    <div class="border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-indigo-300 peer-checked:border-indigo-600 peer-checked:bg-indigo-50 transition-colors">
                                        <div class="flex items-center">
                                            @if($key === 'visa_visiteur')
                                                <i class="fas fa-plane text-blue-600 text-lg mr-3"></i>
                                            @elseif($key === 'assurance_vie')
                                                <i class="fas fa-heart text-red-600 text-lg mr-3"></i>
                                            @elseif($key === 'reer')
                                                <i class="fas fa-piggy-bank text-green-600 text-lg mr-3"></i>
                                            @elseif($key === 'celi')
                                                <i class="fas fa-coins text-yellow-600 text-lg mr-3"></i>
                                            @elseif($key === 'consultation_generale')
                                                <i class="fas fa-comments text-purple-600 text-lg mr-3"></i>
                                            @else
                                                <i class="fas fa-sync text-orange-600 text-lg mr-3"></i>
                                            @endif
                                            <span class="font-medium text-sm">{{ $label }}</span>
                                        </div>
                                    </div>
                                </label>
                                @endforeach
                            </div>
                            @error('service_type')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Sélection de date et heure -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Choisir un créneau</h3>

                            <!-- Calendrier -->
                            <div class="grid md:grid-cols-2 gap-6">
                                <!-- Calendrier -->
                                <div class="bg-white border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <button type="button" id="prevMonth" class="p-2 hover:bg-gray-100 rounded-md">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                        </button>
                                        <div class="text-center">
                                            <h4 id="currentMonth" class="text-lg font-semibold text-gray-900 cursor-pointer hover:text-indigo-600" onclick="showYearSelector()"></h4>
                                            <div id="yearSelector" class="hidden mt-2">
                                                <select id="yearSelect" class="border rounded px-2 py-1 text-sm">
                                                    <!-- Options générées dynamiquement -->
                                                </select>
                                                <button type="button" onclick="goToSelectedYear()" class="ml-2 px-2 py-1 bg-indigo-600 text-white rounded text-xs hover:bg-indigo-700">Aller</button>
                                                <button type="button" onclick="hideYearSelector()" class="ml-1 px-2 py-1 bg-gray-300 text-gray-700 rounded text-xs hover:bg-gray-400">×</button>
                                            </div>
                                        </div>
                                        <button type="button" id="nextMonth" class="p-2 hover:bg-gray-100 rounded-md">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <!-- Jours de la semaine -->
                                    <div class="grid grid-cols-7 gap-1 mb-2">
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Lun</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Mar</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Mer</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Jeu</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Ven</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Sam</div>
                                        <div class="text-center text-sm font-medium text-gray-500 py-2">Dim</div>
                                    </div>

                                    <!-- Grille du calendrier -->
                                    <div id="calendarGrid" class="grid grid-cols-7 gap-1">
                                        <!-- Les jours seront générés par JavaScript -->
                                    </div>
                                </div>

                                <!-- Créneaux horaires -->
                                <div class="bg-white border border-gray-200 rounded-lg p-4">
                                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Créneaux disponibles</h4>
                                    <div id="selectedDateDisplay" class="text-sm text-gray-600 mb-4">
                                        Sélectionnez une date dans le calendrier
                                    </div>
                                    <div id="timeSlots" class="space-y-2">
                                        <!-- Les créneaux seront chargés dynamiquement -->
                                    </div>
                                </div>
                            </div>

                            <!-- Input caché pour stocker la date/heure sélectionnée -->
                            <input type="hidden" name="appointment_date" id="appointmentDateTime" required>
                            @error('appointment_date')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message optionnel -->
                        <div class="mb-8">
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                Message (optionnel)
                            </label>
                            <textarea id="message" name="message" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                      placeholder="Décrivez brièvement vos besoins ou questions...">{{ old('message') }}</textarea>
                            @error('message')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="w-full bg-indigo-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-indigo-700 transition-colors">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Confirmer le rendez-vous
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar Info -->
            <div class="lg:col-span-1">
                <div class="space-y-6">
                    <!-- Contact Info -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-info-circle mr-2 text-indigo-600"></i>
                            Informations pratiques
                        </h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center">
                                <i class="fas fa-clock text-gray-400 mr-3"></i>
                                <span>Durée : 30-45 minutes</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-gift text-gray-400 mr-3"></i>
                                <span>Consultation gratuite</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-map-marker-alt text-gray-400 mr-3"></i>
                                <span>Bureau ou visioconférence</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-phone text-gray-400 mr-3"></i>
                                <span>(*************</span>
                            </div>
                        </div>
                    </div>

                    <!-- What to Expect -->
                    <div class="bg-indigo-50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-indigo-900 mb-4">
                            <i class="fas fa-lightbulb mr-2"></i>
                            À quoi s'attendre ?
                        </h3>
                        <ul class="space-y-2 text-sm text-indigo-800">
                            <li class="flex items-start">
                                <i class="fas fa-check text-indigo-600 mr-2 mt-1"></i>
                                <span>Analyse de vos besoins</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-indigo-600 mr-2 mt-1"></i>
                                <span>Présentation des solutions</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-indigo-600 mr-2 mt-1"></i>
                                <span>Devis personnalisé</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-indigo-600 mr-2 mt-1"></i>
                                <span>Réponses à vos questions</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="bg-red-50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-red-900 mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Besoin urgent ?
                        </h3>
                        <p class="text-sm text-red-800 mb-3">
                            Pour les urgences ou questions immédiates
                        </p>
                        <a href="tel:+15141234567" class="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-red-700 transition-colors">
                            <i class="fas fa-phone mr-2"></i>
                            Appeler maintenant
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    const monthNames = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    let availableSlots = [];

    // Charger les créneaux disponibles depuis l'API pour un mois donné
    async function loadAvailableSlots(year = null, month = null) {
        try {
            // Utiliser l'année/mois actuel du calendrier si non spécifié
            const targetYear = year || currentYear;
            const targetMonth = month || (currentMonth + 1); // +1 car JavaScript compte les mois de 0-11

            const url = `{{ url("/api/appointments/available-slots") }}?year=${targetYear}&month=${targetMonth}`;
            const response = await fetch(url);
            availableSlots = await response.json();
            console.log(`Créneaux chargés pour ${targetMonth}/${targetYear}:`, availableSlots);
            renderCalendar();
        } catch (error) {
            console.error('Erreur lors du chargement des créneaux:', error);
        }
    }

    function renderCalendar() {
        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - (firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1));

        document.getElementById('currentMonth').textContent = `${monthNames[currentMonth]} ${currentYear}`;

        const calendarGrid = document.getElementById('calendarGrid');
        calendarGrid.innerHTML = '';

        for (let i = 0; i < 42; i++) {
            const cellDate = new Date(startDate);
            cellDate.setDate(startDate.getDate() + i);

            const dayElement = document.createElement('div');
            dayElement.className = 'h-10 flex items-center justify-center text-sm cursor-pointer rounded-md transition-colors';
            dayElement.textContent = cellDate.getDate();

            const dateString = cellDate.toISOString().split('T')[0];
            const hasSlots = availableSlots.some(slot => slot.date === dateString);
            const isCurrentMonth = cellDate.getMonth() === currentMonth;
            const isPast = cellDate < new Date().setHours(0, 0, 0, 0);

            // Debug pour voir les dates
            if (isCurrentMonth && !isPast) {
                console.log(`Date: ${dateString}, hasSlots: ${hasSlots}`);
            }

            if (!isCurrentMonth) {
                dayElement.className += ' text-gray-300';
            } else if (isPast) {
                dayElement.className += ' text-gray-400 cursor-not-allowed';
            } else if (hasSlots) {
                dayElement.className += ' text-indigo-600 hover:bg-indigo-50 font-semibold';
                dayElement.addEventListener('click', (e) => selectDate(dateString, e.target));
            } else {
                dayElement.className += ' text-gray-600';
            }

            calendarGrid.appendChild(dayElement);
        }
    }

    function selectDate(dateString, targetElement) {
        console.log('Date sélectionnée:', dateString);
        console.log('Créneaux disponibles:', availableSlots);

        // Retirer la sélection précédente
        document.querySelectorAll('#calendarGrid > div').forEach(el => {
            el.classList.remove('bg-indigo-600', 'text-white');
        });

        // Ajouter la sélection à la date cliquée
        targetElement.classList.add('bg-indigo-600', 'text-white');

        // Trouver les créneaux pour cette date
        const daySlots = availableSlots.find(slot => slot.date === dateString);
        console.log('Créneaux pour cette date:', daySlots);

        if (daySlots) {
            const date = new Date(dateString);
            const formattedDate = date.toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('selectedDateDisplay').textContent = formattedDate;

            const timeSlotsContainer = document.getElementById('timeSlots');
            timeSlotsContainer.innerHTML = '';

            daySlots.times.forEach(timeSlot => {
                const slotElement = document.createElement('label');
                slotElement.className = 'block cursor-pointer';
                slotElement.innerHTML = `
                    <input type="radio" name="time_slot" value="${timeSlot.datetime}" class="sr-only peer" onchange="updateAppointmentDateTime('${timeSlot.datetime}')">
                    <div class="border border-gray-300 rounded-lg p-3 text-center hover:border-indigo-300 peer-checked:border-indigo-600 peer-checked:bg-indigo-50 transition-colors">
                        <span class="font-medium">${timeSlot.formatted}</span>
                    </div>
                `;
                timeSlotsContainer.appendChild(slotElement);
            });
        }
    }

    // Fonction globale pour mettre à jour la date/heure du rendez-vous
    window.updateAppointmentDateTime = function(datetime) {
        console.log('Fonction updateAppointmentDateTime appelée avec:', datetime);
        const element = document.getElementById('appointmentDateTime');
        console.log('Élément trouvé:', element);
        if (element) {
            element.value = datetime;
            console.log('Valeur mise à jour:', element.value);
        } else {
            console.error('Élément appointmentDateTime non trouvé');
        }
    }

    // Navigation du calendrier
    document.getElementById('prevMonth').addEventListener('click', function() {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        loadAvailableSlots(currentYear, currentMonth + 1);
    });

    document.getElementById('nextMonth').addEventListener('click', function() {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        loadAvailableSlots(currentYear, currentMonth + 1);
    });

    // Fonctions pour le sélecteur d'année
    window.showYearSelector = function() {
        const yearSelect = document.getElementById('yearSelect');
        const currentYearValue = new Date().getFullYear();

        // Générer les options d'années (année actuelle + 5 ans)
        yearSelect.innerHTML = '';
        for (let year = currentYearValue; year <= currentYearValue + 5; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year;
            if (year === currentYear) {
                option.selected = true;
            }
            yearSelect.appendChild(option);
        }

        document.getElementById('yearSelector').classList.remove('hidden');
    }

    window.hideYearSelector = function() {
        document.getElementById('yearSelector').classList.add('hidden');
    }

    window.goToSelectedYear = function() {
        const selectedYear = parseInt(document.getElementById('yearSelect').value);
        currentYear = selectedYear;
        currentMonth = 0; // Janvier
        loadAvailableSlots(currentYear, currentMonth + 1);
        hideYearSelector();
    }

    // Initialiser le calendrier
    loadAvailableSlots();
});
</script>

@endsection
