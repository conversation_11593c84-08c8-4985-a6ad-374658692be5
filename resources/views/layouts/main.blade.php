<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Assurance iA Montréal - Votre partenaire assurance')</title>
    <meta name="description" content="@yield('description', 'Assurance iA Montréal - Spécialiste en assurance visa visiteur, assurance vie, REER et CELI. Obtenez votre devis gratuit en ligne.')">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
        .dropdown-enter {
            transition: all 0.1s ease-out;
        }
        .dropdown-enter-start {
            opacity: 0;
            transform: scale(0.95);
        }
        .dropdown-enter-end {
            opacity: 1;
            transform: scale(1);
        }
    </style>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ route('home') }}" class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-shield-alt text-2xl text-indigo-600"></i>
                        </div>
                        <div class="ml-3">
                            <div class="text-lg font-bold text-gray-900">Assurance iA</div>
                            <div class="text-xs text-gray-500">Montréal</div>
                        </div>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('home') ? 'text-indigo-600 border-b-2 border-indigo-600' : '' }}">
                        <i class="fas fa-home mr-1"></i>
                        Accueil
                    </a>

                    <!-- Megamenu Assurances -->
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors flex items-center {{ request()->routeIs('services') ? 'text-indigo-600 border-b-2 border-indigo-600' : '' }}">
                            <i class="fas fa-shield-alt mr-1"></i>
                            Assurances
                            <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>

                        <!-- Dropdown Assurances -->
                        <div class="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                                    <i class="fas fa-shield-alt text-indigo-600 mr-2"></i>
                                    Nos Assurances
                                </h3>
                                <div class="space-y-3">
                                    <a href="{{ route('services') }}#visa-visiteur" class="flex items-center p-3 rounded-lg hover:bg-indigo-50 transition-colors group/item">
                                        <div class="bg-indigo-100 rounded-full w-10 h-10 flex items-center justify-center mr-3 group-hover/item:bg-indigo-200">
                                            <i class="fas fa-plane text-indigo-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Assurance Visa Visiteur</div>
                                            <div class="text-sm text-gray-500">Protection médicale pour vos voyages</div>
                                        </div>
                                    </a>
                                    <a href="{{ route('services') }}#assurance-vie" class="flex items-center p-3 rounded-lg hover:bg-indigo-50 transition-colors group/item">
                                        <div class="bg-indigo-100 rounded-full w-10 h-10 flex items-center justify-center mr-3 group-hover/item:bg-indigo-200">
                                            <i class="fas fa-heart text-indigo-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">Assurance Vie</div>
                                            <div class="text-sm text-gray-500">Protégez votre famille</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-100">
                                    <a href="{{ route('services') }}" class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                                        Voir tous nos services d'assurance →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Megamenu Épargne et Retraite -->
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors flex items-center">
                            <i class="fas fa-piggy-bank mr-1"></i>
                            Épargne et Retraite
                            <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>

                        <!-- Dropdown Épargne -->
                        <div class="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                                    <i class="fas fa-piggy-bank text-indigo-600 mr-2"></i>
                                    Épargne et Investissements
                                </h3>
                                <div class="space-y-3">
                                    <a href="{{ route('services') }}#reer" class="flex items-center p-3 rounded-lg hover:bg-indigo-50 transition-colors group/item">
                                        <div class="bg-indigo-100 rounded-full w-10 h-10 flex items-center justify-center mr-3 group-hover/item:bg-indigo-200">
                                            <i class="fas fa-piggy-bank text-indigo-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">REER</div>
                                            <div class="text-sm text-gray-500">Épargne-retraite avec avantages fiscaux</div>
                                        </div>
                                    </a>
                                    <a href="{{ route('services') }}#celi" class="flex items-center p-3 rounded-lg hover:bg-indigo-50 transition-colors group/item">
                                        <div class="bg-indigo-100 rounded-full w-10 h-10 flex items-center justify-center mr-3 group-hover/item:bg-indigo-200">
                                            <i class="fas fa-university text-indigo-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900">CELI</div>
                                            <div class="text-sm text-gray-500">Compte épargne libre d'impôt</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-100">
                                    <a href="{{ route('services') }}" class="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                                        Découvrir nos solutions d'épargne →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="{{ route('simulation.index') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('simulation.*') ? 'text-indigo-600 border-b-2 border-indigo-600' : '' }}">
                        <i class="fas fa-calculator mr-1"></i>
                        Simulation
                    </a>
                    <a href="{{ route('appointment.create') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('appointment.*') ? 'text-indigo-600 border-b-2 border-indigo-600' : '' }}">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        Rendez-vous
                    </a>
                    <a href="{{ route('contact') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('contact') ? 'text-indigo-600 border-b-2 border-indigo-600' : '' }}">
                        <i class="fas fa-envelope mr-1"></i>
                        Contact
                    </a>

                    @auth
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium focus:outline-none">
                                <i class="fas fa-user mr-2"></i>
                                {{ Auth::user()->name }}
                                <i class="fas fa-chevron-down ml-2 transition-transform duration-200" :class="{ 'rotate-180': open }"></i>
                            </button>

                            <!-- Dropdown menu -->
                            <div x-show="open"
                                 x-cloak
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">

                                <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-tachometer-alt mr-2"></i>
                                    Dashboard Admin
                                </a>

                                <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user-edit mr-2"></i>
                                    Mon Profil
                                </a>

                                <div class="border-t border-gray-100"></div>

                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>
                                        Déconnexion
                                    </button>
                                </form>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium">
                            Connexion
                        </a>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center" x-data="{ mobileOpen: false }">
                    <button @click="mobileOpen = !mobileOpen" type="button" class="text-gray-700 hover:text-indigo-600 focus:outline-none focus:text-indigo-600">
                        <i class="fas fa-bars text-xl" x-show="!mobileOpen"></i>
                        <i class="fas fa-times text-xl" x-show="mobileOpen"></i>
                    </button>

                    <!-- Mobile menu -->
                    <div x-show="mobileOpen"
                         x-cloak
                         @click.away="mobileOpen = false"
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="absolute top-16 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
                        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                            <a href="{{ route('home') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                <i class="fas fa-home mr-2"></i>Accueil
                            </a>

                            <!-- Section Assurances Mobile -->
                            <div class="border-t border-gray-100 pt-2 mt-2">
                                <div class="px-3 py-2 text-sm font-semibold text-indigo-600 uppercase tracking-wide">
                                    <i class="fas fa-shield-alt mr-2"></i>Assurances
                                </div>
                                <a href="{{ route('services') }}#visa-visiteur" class="block px-6 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-plane mr-2 text-sm"></i>Assurance Visa Visiteur
                                </a>
                                <a href="{{ route('services') }}#assurance-vie" class="block px-6 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-heart mr-2 text-sm"></i>Assurance Vie
                                </a>
                            </div>

                            <!-- Section Épargne Mobile -->
                            <div class="border-t border-gray-100 pt-2 mt-2">
                                <div class="px-3 py-2 text-sm font-semibold text-indigo-600 uppercase tracking-wide">
                                    <i class="fas fa-piggy-bank mr-2"></i>Épargne et Retraite
                                </div>
                                <a href="{{ route('services') }}#reer" class="block px-6 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-piggy-bank mr-2 text-sm"></i>REER
                                </a>
                                <a href="{{ route('services') }}#celi" class="block px-6 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-university mr-2 text-sm"></i>CELI
                                </a>
                            </div>

                            <div class="border-t border-gray-100 pt-2 mt-2">
                                <a href="{{ route('simulation.index') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-calculator mr-2"></i>Simulation
                                </a>
                                <a href="{{ route('appointment.create') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-calendar-alt mr-2"></i>Rendez-vous
                                </a>
                                <a href="{{ route('contact') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                    <i class="fas fa-envelope mr-2"></i>Contact
                                </a>
                            </div>
                            @auth
                                <div class="border-t border-gray-200 pt-2">
                                    <a href="{{ route('admin.dashboard') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard Admin
                                    </a>
                                    <a href="{{ route('profile.edit') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">
                                        <i class="fas fa-user-edit mr-2"></i>Mon Profil
                                    </a>
                                    <form method="POST" action="{{ route('logout') }}" class="px-3 py-2">
                                        @csrf
                                        <button type="submit" class="text-left text-base font-medium text-gray-700 hover:text-indigo-600">
                                            <i class="fas fa-sign-out-alt mr-2"></i>Déconnexion
                                        </button>
                                    </form>
                                </div>
                            @else
                                <a href="{{ route('login') }}" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50">Connexion</a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-shield-alt text-2xl text-indigo-400"></i>
                        <div class="ml-3">
                            <div class="text-lg font-bold">Assurance iA Montréal</div>
                            <div class="text-sm text-gray-400">Affilié à iA Groupe financier</div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        Votre partenaire de confiance pour tous vos besoins d'assurance à Montréal.
                        Spécialisés dans l'assurance visa visiteur, assurance vie, REER et CELI.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-indigo-400"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-indigo-400"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-400 hover:text-indigo-400"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Liens rapides</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('services') }}" class="text-gray-300 hover:text-indigo-400">Nos services</a></li>
                        <li><a href="{{ route('simulation.index') }}" class="text-gray-300 hover:text-indigo-400">Simulation gratuite</a></li>
                        <li><a href="{{ route('appointment.create') }}" class="text-gray-300 hover:text-indigo-400">Prendre RDV</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-indigo-400">Contact</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><i class="fas fa-phone mr-2"></i> (*************</li>
                        <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt mr-2"></i> Montréal, QC</li>
                        <li><i class="fas fa-clock mr-2"></i> Lun-Ven: 9h-17h</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    &copy; {{ date('Y') }} Assurance iA Montréal. Tous droits réservés.
                    <a href="{{ route('legal') }}" class="hover:text-indigo-400">Mentions légales</a>
                </p>
            </div>
        </div>
    </footer>



    @stack('scripts')
</body>
</html>
