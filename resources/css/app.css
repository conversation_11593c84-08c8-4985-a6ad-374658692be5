@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animations personnalisées pour les services */
@layer components {
    .service-card {
        @apply transform transition-all duration-300 ease-in-out;
    }

    .service-card:hover {
        @apply scale-105;
    }

    .service-icon {
        @apply transition-transform duration-300 ease-in-out;
    }

    .service-card:hover .service-icon {
        @apply rotate-6 scale-110;
    }

    /* Animation pour les features */
    .feature-item {
        @apply opacity-0 transform translate-x-4 transition-all duration-300;
        animation: slideInLeft 0.6s ease-out forwards;
    }

    .feature-item:nth-child(1) { animation-delay: 0.1s; }
    .feature-item:nth-child(2) { animation-delay: 0.2s; }
    .feature-item:nth-child(3) { animation-delay: 0.3s; }
    .feature-item:nth-child(4) { animation-delay: 0.4s; }
    .feature-item:nth-child(5) { animation-delay: 0.5s; }
}

@keyframes slideInLeft {
    to {
        @apply opacity-100 transform translate-x-0;
    }
}

/* Amélioration du scroll pour les features longues */
.features-scroll {
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb #f9fafb;
}

.features-scroll::-webkit-scrollbar {
    width: 4px;
}

.features-scroll::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded;
}

.features-scroll::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded;
}

.features-scroll::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
}
