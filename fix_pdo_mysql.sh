#!/bin/bash

echo "=== Script pour activer PDO MySQL dans AMPPS ==="
echo ""
echo "Ce script va modifier le fichier php.ini d'AMPPS pour activer l'extension pdo_mysql."
echo "Fichier à modifier : /Applications/AMPPS/apps/php82/etc/php.ini"
echo ""

# Vérifier si le fichier existe
if [ ! -f "/Applications/AMPPS/apps/php82/etc/php.ini" ]; then
    echo "❌ Fichier php.ini non trouvé !"
    exit 1
fi

# Faire une sauvegarde
echo "📋 Création d'une sauvegarde..."
cp /Applications/AMPPS/apps/php82/etc/php.ini /Applications/AMPPS/apps/php82/etc/php.ini.backup

# Vérifier l'état actuel
echo "🔍 État actuel de l'extension pdo_mysql :"
grep -n "pdo_mysql.so" /Applications/AMPPS/apps/php82/etc/php.ini

# Modifier le fichier
echo ""
echo "🔧 Activation de l'extension pdo_mysql..."
sed -i '' 's/;extension=pdo_mysql.so/extension=pdo_mysql.so/' /Applications/AMPPS/apps/php82/etc/php.ini

# Vérifier le résultat
echo ""
echo "✅ Nouvel état de l'extension pdo_mysql :"
grep -n "pdo_mysql.so" /Applications/AMPPS/apps/php82/etc/php.ini

echo ""
echo "🔄 Redémarrez Apache dans AMPPS pour que les changements prennent effet."
echo "📁 Une sauvegarde a été créée : php.ini.backup"
