<?php

require_once 'vendor/autoload.php';

use App\Http\Controllers\HomeController;
use Illuminate\Http\Request;

// Charger l'application Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

try {
    // Créer une instance du contrôleur
    $controller = new HomeController();
    
    // Appeler la méthode index
    $response = $controller->index();
    
    echo "=== Test du contrôleur HomeController ===\n";
    echo "Type de réponse : " . get_class($response) . "\n";
    
    // Vérifier si c'est une vue
    if (method_exists($response, 'getData')) {
        $data = $response->getData();
        echo "Variables passées à la vue :\n";
        foreach ($data as $key => $value) {
            if ($key === 'services') {
                echo "- $key : " . count($value) . " services\n";
                foreach ($value as $service) {
                    echo "  * " . $service->name . "\n";
                }
            } else {
                echo "- $key : " . gettype($value) . "\n";
            }
        }
    }
    
    echo "\n✅ Le contrôleur fonctionne correctement !\n";
    
} catch (Exception $e) {
    echo "❌ Erreur : " . $e->getMessage() . "\n";
    echo "Trace : " . $e->getTraceAsString() . "\n";
}
