<?php

if (!function_exists('setting')) {
    /**
     * Récupère un paramètre du site
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        try {
            $setting = \App\Models\SiteSetting::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
}

if (!function_exists('site_setting')) {
    /**
     * Alias pour setting()
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function site_setting($key, $default = null)
    {
        return setting($key, $default);
    }
}
