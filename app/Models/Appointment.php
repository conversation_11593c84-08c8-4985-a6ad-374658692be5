<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_name',
        'client_email',
        'client_phone',
        'service_type',
        'appointment_date',
        'status',
        'message',
        'admin_notes',
        'reminder_sent'
    ];

    protected $casts = [
        'appointment_date' => 'datetime',
        'reminder_sent' => 'boolean'
    ];

    /**
     * Scope pour les rendez-vous à venir
     */
    public function scopeUpcoming($query)
    {
        return $query->where('appointment_date', '>=', now())
                    ->orderBy('appointment_date', 'asc');
    }

    /**
     * Scope pour les rendez-vous par statut
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope pour les rendez-vous d'aujourd'hui
     */
    public function scopeToday($query)
    {
        return $query->whereDate('appointment_date', Carbon::today());
    }

    /**
     * Vérifie si le rendez-vous est dans le futur
     */
    public function isFuture()
    {
        return $this->appointment_date > Carbon::now();
    }

    /**
     * Formate la date du rendez-vous
     */
    public function getFormattedDateAttribute()
    {
        return $this->appointment_date->format('d/m/Y à H:i');
    }
}
