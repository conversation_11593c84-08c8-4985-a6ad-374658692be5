<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Contract extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_name',
        'client_email',
        'client_phone',
        'contract_number',
        'type',
        'start_date',
        'end_date',
        'amount',
        'status',
        'contract_details',
        'reminder_sent',
        'last_reminder_date',
        'notes'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'last_reminder_date' => 'date',
        'amount' => 'decimal:2',
        'contract_details' => 'array',
        'reminder_sent' => 'boolean'
    ];

    /**
     * Relation avec User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope pour les contrats expirant bientôt
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('end_date', '<=', Carbon::now()->addDays($days))
                    ->where('status', 'active');
    }

    /**
     * Scope pour les contrats actifs
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Vérifie si le contrat expire dans X jours
     */
    public function isExpiringIn($days)
    {
        return $this->end_date->diffInDays(Carbon::now()) <= $days && $this->status === 'active';
    }

    /**
     * Génère un numéro de contrat unique
     */
    public static function generateContractNumber()
    {
        do {
            $number = 'IA-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('contract_number', $number)->exists());

        return $number;
    }
}
