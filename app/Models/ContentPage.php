<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ContentPage extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'template',
        'is_published',
        'meta_title',
        'meta_description',
        'seo_data'
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'seo_data' => 'array'
    ];

    /**
     * Génère automatiquement le slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }
        });

        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }
        });
    }

    /**
     * Scope pour les pages publiées
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope par template
     */
    public function scopeByTemplate($query, $template)
    {
        return $query->where('template', $template);
    }

    /**
     * Trouve une page par son slug
     */
    public static function findBySlug($slug)
    {
        return self::where('slug', $slug)->published()->first();
    }
}
