<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\Service;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Partager les services avec toutes les vues
        View::composer('*', function ($view) {
            $assuranceServices = Service::active()
                ->where('sort_order', '<=', 6)
                ->orderBy('sort_order')
                ->get();
                
            $epargneServices = Service::active()
                ->where('sort_order', '>', 6)
                ->orderBy('sort_order')
                ->get();

            $view->with([
                'assuranceServices' => $assuranceServices,
                'epargneServices' => $epargneServices
            ]);
        });
    }
}
