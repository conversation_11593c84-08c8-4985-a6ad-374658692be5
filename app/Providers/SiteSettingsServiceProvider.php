<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\SiteSetting;

class SiteSettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Partager les paramètres du site avec toutes les vues
        View::composer('*', function ($view) {
            try {
                $settings = SiteSetting::all()->pluck('value', 'key');
                $view->with('siteSettings', $settings);
            } catch (\Exception $e) {
                // En cas d'erreur (ex: migration pas encore exécutée)
                $view->with('siteSettings', collect());
            }
        });

        // Helper global pour récupérer un paramètre
        if (!function_exists('site_setting')) {
            function site_setting($key, $default = null) {
                return SiteSetting::get($key, $default);
            }
        }

        // Helper global pour récupérer un paramètre (alias)
        if (!function_exists('setting')) {
            function setting($key, $default = null) {
                try {
                    $setting = SiteSetting::where('key', $key)->first();
                    return $setting ? $setting->value : $default;
                } catch (\Exception $e) {
                    return $default;
                }
            }
        }
    }
}
