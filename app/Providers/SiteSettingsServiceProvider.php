<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Models\SiteSetting;

class SiteSettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Partager les paramètres du site avec toutes les vues
        View::composer('*', function ($view) {
            try {
                $settings = SiteSetting::all()->pluck('value', 'key');
                $view->with('siteSettings', $settings);
            } catch (\Exception $e) {
                // En cas d'erreur (ex: migration pas encore exécutée)
                $view->with('siteSettings', collect());
            }
        });

        // Les helpers sont maintenant définis dans app/helpers.php
    }
}
