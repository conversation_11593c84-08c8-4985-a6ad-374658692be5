<?php

namespace App\Services;

use Twilio\Rest\Client;
use Illuminate\Support\Facades\Log;

class SmsService
{
    protected $client;
    protected $from;

    public function __construct()
    {
        $sid = config('services.twilio.sid');
        $token = config('services.twilio.token');
        $this->from = config('services.twilio.from');

        if ($sid && $token) {
            $this->client = new Client($sid, $token);
        }
    }

    /**
     * Envoie un SMS
     *
     * @param string $to Numéro de téléphone destinataire
     * @param string $message Message à envoyer
     * @return bool
     */
    public function send($to, $message)
    {
        if (!$this->client) {
            Log::warning('Service SMS non configuré - Twilio SID/Token manquants');
            return false;
        }

        if (!$this->from) {
            Log::warning('Service SMS non configuré - Numéro Twilio manquant');
            return false;
        }

        try {
            // Nettoyer et formater le numéro de téléphone
            $to = $this->formatPhoneNumber($to);

            $message = $this->client->messages->create(
                $to,
                [
                    'from' => $this->from,
                    'body' => $message
                ]
            );

            Log::info("SMS envoyé avec succès", [
                'to' => $to,
                'sid' => $message->sid,
                'status' => $message->status
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Erreur lors de l'envoi du SMS", [
                'to' => $to,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Envoie un SMS de rappel de contrat
     *
     * @param \App\Models\Contract $contract
     * @return bool
     */
    public function sendContractReminder($contract)
    {
        $message = "Bonjour {$contract->client_name}, votre contrat d'assurance #{$contract->contract_number} expire le {$contract->end_date->format('d/m/Y')}. Contactez Assurance iA Montréal pour le renouveler : (*************";

        return $this->send($contract->client_phone, $message);
    }

    /**
     * Envoie un SMS de confirmation de rendez-vous
     *
     * @param \App\Models\Appointment $appointment
     * @return bool
     */
    public function sendAppointmentConfirmation($appointment)
    {
        $message = "Bonjour {$appointment->client_name}, votre rendez-vous avec Assurance iA Montréal est confirmé pour le {$appointment->appointment_date->format('d/m/Y à H:i')}. Bureau : (*************";

        return $this->send($appointment->client_phone, $message);
    }

    /**
     * Envoie un SMS de rappel de rendez-vous
     *
     * @param \App\Models\Appointment $appointment
     * @return bool
     */
    public function sendAppointmentReminder($appointment)
    {
        $message = "Rappel : Votre rendez-vous avec Assurance iA Montréal est prévu demain le {$appointment->appointment_date->format('d/m/Y à H:i')}. Pour annuler ou reporter : (*************";

        return $this->send($appointment->client_phone, $message);
    }

    /**
     * Formate un numéro de téléphone pour Twilio
     *
     * @param string $phoneNumber
     * @return string
     */
    private function formatPhoneNumber($phoneNumber)
    {
        // Supprimer tous les caractères non numériques
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Si le numéro commence par 1, on le garde
        if (strlen($cleaned) === 11 && substr($cleaned, 0, 1) === '1') {
            return '+' . $cleaned;
        }

        // Si le numéro fait 10 chiffres, on ajoute +1 (Canada/US)
        if (strlen($cleaned) === 10) {
            return '+1' . $cleaned;
        }

        // Sinon, on ajoute juste le +
        return '+' . $cleaned;
    }

    /**
     * Vérifie si le service SMS est configuré
     *
     * @return bool
     */
    public function isConfigured()
    {
        return $this->client !== null && $this->from !== null;
    }

    /**
     * Obtient le solde du compte Twilio
     *
     * @return array|null
     */
    public function getAccountBalance()
    {
        if (!$this->client) {
            return null;
        }

        try {
            $account = $this->client->api->v2010->accounts(config('services.twilio.sid'))->fetch();
            
            return [
                'balance' => $account->balance,
                'currency' => 'USD'
            ];
        } catch (\Exception $e) {
            Log::error("Erreur lors de la récupération du solde Twilio", [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}
