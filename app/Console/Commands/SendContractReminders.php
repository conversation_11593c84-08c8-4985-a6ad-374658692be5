<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Contract;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContractReminderMail;
use App\Services\SmsService;
use Carbon\Carbon;

class SendContractReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contracts:send-reminders {--days=7 : Number of days before expiration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email and SMS reminders for contracts expiring soon';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $targetDate = Carbon::now()->addDays($days)->toDateString();

        $this->info("Recherche des contrats expirant le {$targetDate}...");

        // Récupérer les contrats expirant dans X jours
        $expiringContracts = Contract::where('end_date', $targetDate)
            ->where('status', 'active')
            ->where('reminder_sent', false)
            ->get();

        if ($expiringContracts->isEmpty()) {
            $this->info('Aucun contrat à rappeler aujourd\'hui.');
            return;
        }

        $this->info("Trouvé {$expiringContracts->count()} contrat(s) à rappeler.");

        $emailsSent = 0;
        $smsSent = 0;
        $errors = 0;

        foreach ($expiringContracts as $contract) {
            try {
                // Envoyer l'email de rappel
                Mail::to($contract->client_email)->send(
                    new ContractReminderMail($contract)
                );
                $emailsSent++;

                // Envoyer le SMS si le service est configuré
                if (config('services.twilio.sid')) {
                    $smsService = new SmsService();
                    $message = "Bonjour {$contract->client_name}, votre contrat d'assurance #{$contract->contract_number} expire le {$contract->end_date->format('d/m/Y')}. Contactez-nous pour le renouveler : (*************";

                    if ($smsService->send($contract->client_phone, $message)) {
                        $smsSent++;
                    }
                }

                // Marquer le rappel comme envoyé
                $contract->update([
                    'reminder_sent' => true,
                    'last_reminder_date' => Carbon::now()
                ]);

                $this->line("✓ Rappel envoyé pour le contrat #{$contract->contract_number} ({$contract->client_name})");

            } catch (\Exception $e) {
                $errors++;
                $this->error("✗ Erreur pour le contrat #{$contract->contract_number}: " . $e->getMessage());
            }
        }

        // Résumé
        $this->info("\n=== RÉSUMÉ ===");
        $this->info("Emails envoyés: {$emailsSent}");
        $this->info("SMS envoyés: {$smsSent}");
        if ($errors > 0) {
            $this->error("Erreurs: {$errors}");
        }
        $this->info("Terminé !");
    }
}
