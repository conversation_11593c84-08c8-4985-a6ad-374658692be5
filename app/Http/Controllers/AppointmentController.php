<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Appointment;
use Illuminate\Support\Facades\Mail;
use App\Mail\AppointmentConfirmationMail;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    /**
     * Affiche le formulaire de prise de rendez-vous
     */
    public function create()
    {
        $services = [
            'visa_visiteur' => 'Assurance Visa Visiteur',
            'assurance_vie' => 'Assurance Vie',
            'reer' => 'REER',
            'celi' => 'CELI',
            'consultation_generale' => 'Consultation générale',
            'renouvellement' => 'Renouvellement de contrat'
        ];

        // Générer les créneaux disponibles pour les 30 prochains jours
        $availableSlots = $this->generateAvailableSlots();

        return view('appointments.create', compact('services', 'availableSlots'));
    }

    /**
     * Enregistre le rendez-vous
     */
    public function store(Request $request)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'client_email' => 'required|email|max:255',
            'client_phone' => 'required|string|max:20',
            'service_type' => 'required|string',
            'appointment_date' => 'required|date|after:now',
            'message' => 'nullable|string|max:1000'
        ]);

        // Vérifier que le créneau est encore disponible
        $appointmentDateTime = Carbon::parse($request->appointment_date);
        $existingAppointment = Appointment::where('appointment_date', $appointmentDateTime)
            ->whereIn('status', ['pending', 'confirmed'])
            ->first();

        if ($existingAppointment) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Ce créneau n\'est plus disponible. Veuillez choisir un autre horaire.');
        }

        // Créer le rendez-vous
        $appointment = Appointment::create([
            'client_name' => $request->client_name,
            'client_email' => $request->client_email,
            'client_phone' => $request->client_phone,
            'service_type' => $request->service_type,
            'appointment_date' => $appointmentDateTime,
            'message' => $request->message,
            'status' => 'pending'
        ]);

        try {
            // Envoyer l'email de confirmation
            Mail::to($request->client_email)->send(
                new AppointmentConfirmationMail($appointment)
            );

            // Envoyer une notification à l'admin
            Mail::to('<EMAIL>')->send(
                new AppointmentConfirmationMail($appointment, true)
            );
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer la création du RDV
            \Log::error('Erreur envoi email RDV: ' . $e->getMessage());
        }

        return redirect()->route('appointment.success')
            ->with('appointment_id', $appointment->id);
    }

    /**
     * Affiche la page de confirmation
     */
    public function success()
    {
        $appointmentId = session('appointment_id');
        if (!$appointmentId) {
            return redirect()->route('appointment.create');
        }

        $appointment = Appointment::find($appointmentId);
        return view('appointments.success', compact('appointment'));
    }

    /**
     * API pour récupérer les créneaux disponibles
     */
    public function getAvailableSlots()
    {
        $slots = $this->generateAvailableSlots();
        return response()->json($slots);
    }

    /**
     * Génère les créneaux disponibles
     */
    private function generateAvailableSlots()
    {
        $slots = [];
        $startDate = Carbon::now()->addDay(); // Commencer demain
        $endDate = Carbon::now()->addDays(90); // 3 mois à l'avance

        // Debug
        \Log::info('Génération des créneaux du ' . $startDate->format('Y-m-d') . ' au ' . $endDate->format('Y-m-d'));

        // Heures d'ouverture : 9h-17h, du lundi au vendredi
        $workingHours = [
            '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'
        ];

        // Récupérer tous les rendez-vous existants en une seule requête
        $bookedSlots = Appointment::whereBetween('appointment_date', [$startDate, $endDate])
            ->whereIn('status', ['pending', 'confirmed'])
            ->pluck('appointment_date')
            ->map(function($date) {
                return \Carbon\Carbon::parse($date)->format('Y-m-d H:i:s');
            })
            ->toArray();

        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            // Seulement les jours ouvrables (lundi à vendredi)
            if ($currentDate->isWeekday()) {
                foreach ($workingHours as $hour) {
                    $slotDateTime = $currentDate->copy()->setTimeFromTimeString($hour);

                    // Vérifier si le créneau est libre
                    $isBooked = in_array($slotDateTime->format('Y-m-d H:i:s'), $bookedSlots);

                    if (!$isBooked) {
                        $dateKey = $currentDate->format('Y-m-d');
                        if (!isset($slots[$dateKey])) {
                            $slots[$dateKey] = [
                                'date' => $currentDate->format('Y-m-d'),
                                'date_formatted' => $currentDate->locale('fr')->isoFormat('dddd D MMMM YYYY'),
                                'times' => []
                            ];
                        }
                        $slots[$dateKey]['times'][] = [
                            'time' => $hour,
                            'datetime' => $slotDateTime->format('Y-m-d H:i:s'),
                            'formatted' => $slotDateTime->format('H:i')
                        ];
                    }
                }
            }
            $currentDate->addDay();
        }

        \Log::info('Nombre de jours avec créneaux générés: ' . count($slots));
        return array_values($slots);
    }
}
