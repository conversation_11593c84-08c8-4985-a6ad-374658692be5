<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SimulationLead;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Mail\SimulationResultMail;
use Dompdf\Dompdf;
use Dompdf\Options;

class SimulationController extends Controller
{
    /**
     * Affiche le formulaire de simulation
     */
    public function index(Request $request)
    {
        $simulationTypes = [
            'assurance' => 'Simulation d\'Assurance',
            'epargne' => 'Simulation d\'Épargne et Retraite'
        ];

        // Pré-sélectionner un type si passé en paramètre
        $selectedType = $request->get('type');

        // Si un service est spécifié, déterminer la catégorie
        if ($request->has('service')) {
            $service = \App\Models\Service::where('slug', $request->get('service'))->first();
            if ($service) {
                $selectedType = $service->category; // 'assurance' ou 'epargne'
            }
        }

        return view('simulation.index', compact('simulationTypes', 'selectedType'));
    }

    /**
     * Calcule la simulation
     */
    public function calculate(Request $request)
    {
        $request->validate([
            'simulation_type' => 'required|in:assurance,epargne',
            'age' => 'required|integer|min:18|max:100',
            'coverage_amount' => 'required|numeric|min:1000',
            'duration' => 'required|integer|min:1'
        ]);

        // Calcul de la prime selon le type de simulation
        $estimatedAmount = $this->calculatePremium($request->all());

        // Stocker les données de simulation en session
        Session::put('simulation_data', [
            'simulation_type' => $request->simulation_type,
            'age' => $request->age,
            'coverage_amount' => $request->coverage_amount,
            'duration' => $request->duration,
            'estimated_amount' => $estimatedAmount
        ]);

        return redirect()->route('simulation.lead-info');
    }

    /**
     * Affiche le formulaire de capture de leads
     */
    public function leadInfo()
    {
        if (!Session::has('simulation_data')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Veuillez d\'abord effectuer une simulation.');
        }

        return view('simulation.lead-info');
    }

    /**
     * Stocke les informations du lead
     */
    public function storeLead(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20'
        ]);

        if (!Session::has('simulation_data')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Session expirée. Veuillez recommencer la simulation.');
        }

        $simulationData = Session::get('simulation_data');

        // Créer le lead
        $lead = SimulationLead::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'simulation_data' => $simulationData,
            'insurance_type' => $simulationData['simulation_type'], // Utilise simulation_type comme insurance_type
            'estimated_amount' => $simulationData['estimated_amount']
        ]);

        Session::put('lead_id', $lead->id);

        return redirect()->route('simulation.result');
    }

    /**
     * Affiche le résultat de la simulation
     */
    public function result()
    {
        if (!Session::has('simulation_data') || !Session::has('lead_id')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Session expirée. Veuillez recommencer la simulation.');
        }

        $simulationData = Session::get('simulation_data');
        $leadId = Session::get('lead_id');
        $lead = SimulationLead::find($leadId);

        return view('simulation.result', compact('simulationData', 'lead'));
    }

    /**
     * Calcule la prime selon le type de simulation
     */
    private function calculatePremium($data)
    {
        $baseRate = 0;
        $ageMultiplier = 1;

        switch ($data['simulation_type']) {
            case 'assurance':
                // Calcul générique pour toutes les assurances
                $baseRate = 0.8; // $ par mois par 1000$ de couverture
                $ageMultiplier = 1 + (($data['age'] - 25) * 0.02);
                return ($data['coverage_amount'] / 1000) * $baseRate * 12 * $ageMultiplier;

            case 'epargne':
                // Calcul générique pour épargne et retraite
                return $data['coverage_amount'] * 0.015; // 1.5% de frais de gestion annuels

            default:
                return 0;
        }
    }

    /**
     * Génère un PDF de la simulation
     */
    public function generatePdf($id)
    {
        $lead = SimulationLead::findOrFail($id);

        $options = new Options();
        $options->set('defaultFont', 'Arial');
        $dompdf = new Dompdf($options);

        $html = view('simulation.pdf', compact('lead'))->render();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return $dompdf->stream('simulation-' . $lead->id . '.pdf');
    }


}
