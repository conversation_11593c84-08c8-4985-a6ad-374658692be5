<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SimulationLead;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Mail\SimulationResultMail;
use Dompdf\Dompdf;
use Dompdf\Options;

class SimulationController extends Controller
{
    /**
     * Affiche le formulaire de simulation
     */
    public function index(Request $request)
    {
        // Récupérer tous les services actifs par catégorie
        $assuranceServices = \App\Models\Service::active()
            ->where('category', 'assurance')
            ->ordered()
            ->get();

        $epargneServices = \App\Models\Service::active()
            ->where('category', 'epargne')
            ->ordered()
            ->get();

        // Pré-sélectionner un service si passé en paramètre
        $selectedService = $request->get('service');

        return view('simulation.index', compact('assuranceServices', 'epargneServices', 'selectedService'));
    }

    /**
     * Calcule la simulation
     */
    public function calculate(Request $request)
    {
        $request->validate([
            'insurance_type' => 'required|string',
            'age' => 'required|integer|min:18|max:100',
            'coverage_amount' => 'required|numeric|min:1000',
            'duration' => 'required|integer|min:1',
            'country' => 'required_if:insurance_type,visa_visiteur|string|nullable'
        ]);

        // Calcul de la prime selon le type d'assurance
        $estimatedAmount = $this->calculatePremium($request->all());

        // Stocker les données de simulation en session
        Session::put('simulation_data', [
            'insurance_type' => $request->insurance_type,
            'age' => $request->age,
            'coverage_amount' => $request->coverage_amount,
            'duration' => $request->duration,
            'country' => $request->country,
            'estimated_amount' => $estimatedAmount
        ]);

        return redirect()->route('simulation.lead-info');
    }

    /**
     * Affiche le formulaire de capture de leads
     */
    public function leadInfo()
    {
        if (!Session::has('simulation_data')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Veuillez d\'abord effectuer une simulation.');
        }

        return view('simulation.lead-info');
    }

    /**
     * Stocke les informations du lead
     */
    public function storeLead(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20'
        ]);

        if (!Session::has('simulation_data')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Session expirée. Veuillez recommencer la simulation.');
        }

        $simulationData = Session::get('simulation_data');

        // Créer le lead
        $lead = SimulationLead::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'simulation_data' => $simulationData,
            'insurance_type' => $simulationData['insurance_type'],
            'estimated_amount' => $simulationData['estimated_amount']
        ]);

        Session::put('lead_id', $lead->id);

        return redirect()->route('simulation.result');
    }

    /**
     * Affiche le résultat de la simulation
     */
    public function result()
    {
        if (!Session::has('simulation_data') || !Session::has('lead_id')) {
            return redirect()->route('simulation.index')
                ->with('error', 'Session expirée. Veuillez recommencer la simulation.');
        }

        $simulationData = Session::get('simulation_data');
        $leadId = Session::get('lead_id');
        $lead = SimulationLead::find($leadId);

        return view('simulation.result', compact('simulationData', 'lead'));
    }

    /**
     * Calcule la prime d'assurance
     */
    private function calculatePremium($data)
    {
        $baseRate = 0;
        $ageMultiplier = 1;
        $durationMultiplier = 1;

        switch ($data['insurance_type']) {
            case 'visa_visiteur':
                $baseRate = 2.5; // $ par jour par 1000$ de couverture
                $ageMultiplier = $data['age'] > 65 ? 2.5 : ($data['age'] > 50 ? 1.8 : 1);
                return ($data['coverage_amount'] / 1000) * $baseRate * $data['duration'] * $ageMultiplier;

            case 'assurance_vie':
                $baseRate = 0.8; // $ par mois par 1000$ de couverture
                $ageMultiplier = 1 + (($data['age'] - 25) * 0.02);
                return ($data['coverage_amount'] / 1000) * $baseRate * 12 * $ageMultiplier;

            case 'reer':
                return $data['coverage_amount'] * 0.015; // 1.5% de frais de gestion annuels

            case 'celi':
                return $data['coverage_amount'] * 0.012; // 1.2% de frais de gestion annuels

            default:
                return 0;
        }
    }

    /**
     * Génère un PDF de la simulation
     */
    public function generatePdf($id)
    {
        $lead = SimulationLead::findOrFail($id);

        $options = new Options();
        $options->set('defaultFont', 'Arial');
        $dompdf = new Dompdf($options);

        $html = view('simulation.pdf', compact('lead'))->render();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return $dompdf->stream('simulation-' . $lead->id . '.pdf');
    }

    /**
     * Mappe un service vers un type de simulation
     */
    private function mapServiceToSimulationType($serviceSlug)
    {
        $serviceMapping = [
            // Assurances -> assurance_vie (générique)
            'assurance-hypothecaire' => 'assurance_vie',
            'assurance-vie' => 'assurance_vie',
            'assurance-invalidite' => 'assurance_vie',
            'assurance-maladie-grave' => 'assurance_vie',
            'assurance-voyage-medicale' => 'visa_visiteur',
            'assurance-etudiant-etranger' => 'visa_visiteur',
            'assurance-super-visa' => 'visa_visiteur',

            // Épargne -> types spécifiques
            'celi' => 'celi',
            'reer' => 'reer',
            'ferr' => 'reer', // FERR utilise la même logique que REER
            'frv-rentes' => 'reer',
            'placements-garantis' => 'celi', // Utilise la logique CELI
        ];

        return $serviceMapping[$serviceSlug] ?? null;
    }
}
