<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    public function getAvailableSlots(Request $request)
    {
        $startDate = $request->get('start_date', now()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->addDays(30)->format('Y-m-d'));

        $availableSlots = [];
        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        while ($current <= $end) {
            // Ignorer les weekends pour cet exemple
            if ($current->isWeekday()) {
                $slots = [];

                // Créneaux du matin (9h-12h)
                for ($hour = 9; $hour < 12; $hour++) {
                    $slots[] = [
                        'datetime' => $current->format('Y-m-d') . ' ' . sprintf('%02d:00:00', $hour),
                        'formatted' => sprintf('%02d:00', $hour)
                    ];
                    $slots[] = [
                        'datetime' => $current->format('Y-m-d') . ' ' . sprintf('%02d:30:00', $hour),
                        'formatted' => sprintf('%02d:30', $hour)
                    ];
                }

                // Créneaux de l'après-midi (14h-17h)
                for ($hour = 14; $hour < 17; $hour++) {
                    $slots[] = [
                        'datetime' => $current->format('Y-m-d') . ' ' . sprintf('%02d:00:00', $hour),
                        'formatted' => sprintf('%02d:00', $hour)
                    ];
                    $slots[] = [
                        'datetime' => $current->format('Y-m-d') . ' ' . sprintf('%02d:30:00', $hour),
                        'formatted' => sprintf('%02d:30', $hour)
                    ];
                }

                $availableSlots[] = [
                    'date' => $current->format('Y-m-d'),
                    'date_formatted' => $current->locale('fr')->isoFormat('dddd DD MMMM YYYY'),
                    'times' => $slots
                ];
            }

            $current->addDay();
        }

        return response()->json($availableSlots);
    }
}
