<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormMail;
use App\Models\Service;

class HomeController extends Controller
{
    /**
     * Page d'accueil
     */
    public function index()
    {
        $assuranceServices = Service::active()
            ->where('category', 'assurance')
            ->ordered()
            ->get();

        $epargneServices = Service::active()
            ->where('category', 'epargne')
            ->ordered()
            ->get();

        return view('home.index', compact('assuranceServices', 'epargneServices'));
    }

    /**
     * Page des services
     */
    public function services()
    {
        return view('home.services');
    }

    /**
     * Page de contact
     */
    public function contact()
    {
        return view('home.contact');
    }

    /**
     * Traitement du formulaire de contact
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000'
        ]);

        try {
            // Envoyer l'email de contact
            Mail::to('<EMAIL>')->send(
                new ContactFormMail($request->all())
            );

            return redirect()->route('contact')->with('success',
                'Votre message a été envoyé avec succès. Nous vous contacterons sous peu.');
        } catch (\Exception $e) {
            return redirect()->route('contact')->with('error',
                'Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer.');
        }
    }

    /**
     * Page des mentions légales
     */
    public function legal()
    {
        return view('home.legal');
    }
}
