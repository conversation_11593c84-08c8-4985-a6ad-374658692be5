<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContentPage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pages = ContentPage::orderBy('title')->paginate(20);
        return view('admin.content.index', compact('pages'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.content.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'template' => 'nullable|string|max:100',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['is_published'] = $request->has('is_published');

        ContentPage::create($data);

        return redirect()->route('admin.content.index')
            ->with('success', 'Page créée avec succès.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ContentPage $content)
    {
        return view('admin.content.edit', compact('content'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContentPage $content)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'template' => 'nullable|string|max:100',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500'
        ]);

        $data = $request->all();
        $data['is_published'] = $request->has('is_published');

        $content->update($data);

        return redirect()->route('admin.content.index')
            ->with('success', 'Page mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContentPage $content)
    {
        $content->delete();

        return redirect()->route('admin.content.index')
            ->with('success', 'Page supprimée avec succès.');
    }
}
