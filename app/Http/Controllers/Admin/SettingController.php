<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Affiche les paramètres du site
     */
    public function index()
    {
        $settings = SiteSetting::orderBy('group')->orderBy('label')->get()->groupBy('group');
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Met à jour les paramètres
     */
    public function update(Request $request)
    {
        foreach ($request->except(['_token', '_method']) as $key => $value) {
            $setting = SiteSetting::where('key', $key)->first();

            if ($setting) {
                // Cast la valeur selon le type
                if ($setting->type === 'boolean') {
                    $value = $request->has($key) ? '1' : '0';
                } elseif ($setting->type === 'json' && is_array($value)) {
                    $value = json_encode($value);
                }

                $setting->update(['value' => $value]);
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Paramètres mis à jour avec succès.');
    }

    /**
     * Crée un nouveau paramètre
     */
    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required|string|unique:site_settings,key',
            'label' => 'required|string',
            'value' => 'nullable|string',
            'type' => 'required|in:text,textarea,number,boolean,json',
            'group' => 'required|string',
            'description' => 'nullable|string'
        ]);

        SiteSetting::create($request->all());

        return redirect()->route('admin.settings.index')
            ->with('success', 'Paramètre créé avec succès.');
    }

    /**
     * Supprime un paramètre
     */
    public function destroy(SiteSetting $setting)
    {
        $setting->delete();

        return redirect()->route('admin.settings.index')
            ->with('success', 'Paramètre supprimé avec succès.');
    }
}
