<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;

class HomeContentController extends Controller
{
    /**
     * Affiche le formulaire d'édition du contenu de la page d'accueil
     */
    public function edit()
    {
        // Récupérer tous les paramètres de contenu de la page d'accueil
        $homeContent = SiteSetting::where('group', 'home_content')->get()->keyBy('key');
        
        return view('admin.home-content.edit', compact('homeContent'));
    }

    /**
     * Met à jour le contenu de la page d'accueil
     */
    public function update(Request $request)
    {
        $request->validate([
            'hero_title' => 'required|string|max:255',
            'hero_subtitle' => 'required|string|max:500',
            'hero_description' => 'required|string|max:1000',
            'services_title' => 'required|string|max:255',
            'services_description' => 'required|string|max:500',
            'why_choose_title' => 'required|string|max:255',
            'why_choose_description' => 'required|string|max:500',
            'cta_title' => 'required|string|max:255',
            'cta_description' => 'required|string|max:500',
            'company_name' => 'required|string|max:255',
            'company_tagline' => 'required|string|max:255',
        ]);

        // Définir les paramètres de contenu de la page d'accueil
        $homeSettings = [
            'hero_title' => [
                'label' => 'Titre principal Hero',
                'value' => $request->hero_title,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'hero_subtitle' => [
                'label' => 'Sous-titre Hero',
                'value' => $request->hero_subtitle,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'hero_description' => [
                'label' => 'Description Hero',
                'value' => $request->hero_description,
                'type' => 'textarea',
                'group' => 'home_content'
            ],
            'services_title' => [
                'label' => 'Titre section services',
                'value' => $request->services_title,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'services_description' => [
                'label' => 'Description section services',
                'value' => $request->services_description,
                'type' => 'textarea',
                'group' => 'home_content'
            ],
            'why_choose_title' => [
                'label' => 'Titre "Pourquoi nous choisir"',
                'value' => $request->why_choose_title,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'why_choose_description' => [
                'label' => 'Description "Pourquoi nous choisir"',
                'value' => $request->why_choose_description,
                'type' => 'textarea',
                'group' => 'home_content'
            ],
            'cta_title' => [
                'label' => 'Titre Call-to-Action',
                'value' => $request->cta_title,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'cta_description' => [
                'label' => 'Description Call-to-Action',
                'value' => $request->cta_description,
                'type' => 'textarea',
                'group' => 'home_content'
            ],
            'company_name' => [
                'label' => 'Nom de l\'entreprise',
                'value' => $request->company_name,
                'type' => 'text',
                'group' => 'home_content'
            ],
            'company_tagline' => [
                'label' => 'Slogan de l\'entreprise',
                'value' => $request->company_tagline,
                'type' => 'text',
                'group' => 'home_content'
            ],
        ];

        // Créer ou mettre à jour chaque paramètre
        foreach ($homeSettings as $key => $data) {
            SiteSetting::updateOrCreate(
                ['key' => $key],
                $data
            );
        }

        return redirect()->route('admin.home-content.edit')
            ->with('success', 'Contenu de la page d\'accueil mis à jour avec succès.');
    }
}
