<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SimulationLead;
use App\Models\Contract;
use App\Models\Appointment;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Dashboard admin
     */
    public function dashboard()
    {
        $stats = [
            'leads_today' => SimulationLead::whereDate('created_at', Carbon::today())->count(),
            'leads_week' => SimulationLead::whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])->count(),
            'appointments_today' => Appointment::whereDate('appointment_date', Carbon::today())->count(),
            'contracts_expiring' => Contract::expiringSoon(30)->count(),
        ];

        $recentLeads = SimulationLead::recent()->take(5)->get();
        $upcomingAppointments = Appointment::upcoming()->take(5)->get();
        $expiringContracts = Contract::expiringSoon(30)->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recentLeads', 'upcomingAppointments', 'expiringContracts'));
    }

    /**
     * Gestion des leads
     */
    public function leads(Request $request)
    {
        $query = SimulationLead::query();

        // Filtres
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('insurance_type')) {
            $query->where('insurance_type', $request->insurance_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $leads = $query->recent()->paginate(20);

        return view('admin.leads', compact('leads'));
    }

    /**
     * Gestion des contrats
     */
    public function contracts(Request $request)
    {
        $query = Contract::query();

        // Filtres
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('expiring_soon')) {
            $query->expiringSoon(30);
        }

        $contracts = $query->orderBy('end_date', 'asc')->paginate(20);

        return view('admin.contracts', compact('contracts'));
    }

    /**
     * Gestion des rendez-vous
     */
    public function appointments(Request $request)
    {
        $query = Appointment::query();

        // Filtres
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date')) {
            $query->whereDate('appointment_date', $request->date);
        }

        if ($request->filled('service_type')) {
            $query->where('service_type', $request->service_type);
        }

        $appointments = $query->orderBy('appointment_date', 'asc')->paginate(20);

        return view('admin.appointments', compact('appointments'));
    }

    /**
     * Mise à jour d'un lead
     */
    public function updateLead(Request $request, SimulationLead $lead)
    {
        $request->validate([
            'status' => 'required|in:new,contacted,converted,lost',
            'notes' => 'nullable|string|max:1000'
        ]);

        $lead->update([
            'status' => $request->status,
            'notes' => $request->notes
        ]);

        return redirect()->back()->with('success', 'Lead mis à jour avec succès.');
    }

    /**
     * Mise à jour d'un rendez-vous
     */
    public function updateAppointment(Request $request, Appointment $appointment)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,completed,cancelled',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $appointment->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ]);

        return redirect()->back()->with('success', 'Rendez-vous mis à jour avec succès.');
    }
}
