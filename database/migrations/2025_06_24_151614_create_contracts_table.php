<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('client_name'); // Nom du client
            $table->string('client_email');
            $table->string('client_phone');
            $table->string('contract_number')->unique();
            $table->enum('type', ['visa_visiteur', 'assurance_vie', 'reer', 'celi', 'autre']);
            $table->date('start_date');
            $table->date('end_date');
            $table->decimal('amount', 10, 2);
            $table->enum('status', ['active', 'expired', 'cancelled', 'renewed'])->default('active');
            $table->json('contract_details')->nullable(); // Détails spécifiques du contrat
            $table->boolean('reminder_sent')->default(false);
            $table->date('last_reminder_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['end_date', 'status']);
            $table->index('client_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
