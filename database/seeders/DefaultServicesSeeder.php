<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Service;

class DefaultServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Assurance Visa Visiteur',
                'slug' => 'visa-visiteur',
                'description' => 'Protection médicale complète pour vos voyages au Canada',
                'detailed_description' => 'Notre assurance visa visiteur offre une couverture médicale complète pour tous vos séjours au Canada. Que vous veniez pour le tourisme, les affaires ou rendre visite à votre famille, nous vous protégeons contre les frais médicaux imprévus.',
                'icon' => 'fas fa-plane',
                'features' => [
                    'Soins médicaux d\'urgence',
                    'Hospitalisation et chirurgie',
                    'Médicaments prescrits',
                    'Services d\'ambulance',
                    'Rapatriement médical',
                    'Assistance voyage 24h/24',
                    'Couverture dentaire d\'urgence'
                ],
                'base_price' => 2.50,
                'pricing_rules' => [
                    'base_rate_per_day_per_1000' => 2.5,
                    'age_multipliers' => [
                        'under_50' => 1.0,
                        '50_to_65' => 1.8,
                        'over_65' => 2.5
                    ]
                ],
                'is_active' => true,
                'sort_order' => 1,
                'meta_title' => 'Assurance Visa Visiteur Canada - Protection Médicale Voyage',
                'meta_description' => 'Assurance médicale complète pour vos voyages au Canada. Couverture d\'urgence, hospitalisation, médicaments. Devis gratuit en ligne.'
            ],
            [
                'name' => 'Assurance Vie',
                'slug' => 'assurance-vie',
                'description' => 'Protégez votre famille avec notre assurance vie adaptée à vos besoins',
                'detailed_description' => 'Notre assurance vie vous permet de protéger financièrement votre famille en cas de décès. Avec des options flexibles et des primes abordables, nous vous aidons à choisir la couverture qui convient à votre situation.',
                'icon' => 'fas fa-heart',
                'features' => [
                    'Capital décès garanti',
                    'Primes nivelées',
                    'Options d\'avenants',
                    'Valeur de rachat',
                    'Examen médical simplifié',
                    'Protection de la famille',
                    'Conseils personnalisés'
                ],
                'base_price' => 0.80,
                'pricing_rules' => [
                    'base_rate_per_month_per_1000' => 0.8,
                    'age_factor' => 0.02,
                    'minimum_age' => 25
                ],
                'is_active' => true,
                'sort_order' => 2,
                'meta_title' => 'Assurance Vie Montréal - Protection Famille iA',
                'meta_description' => 'Assurance vie temporaire et permanente. Protégez votre famille avec des primes abordables. Simulation gratuite et conseils d\'experts.'
            ],
            [
                'name' => 'REER',
                'slug' => 'reer',
                'description' => 'Régime enregistré d\'épargne-retraite pour optimiser votre fiscalité',
                'detailed_description' => 'Nos REER vous permettent d\'épargner pour votre retraite tout en bénéficiant d\'avantages fiscaux immédiats. Déduction d\'impôt, croissance à l\'abri de l\'impôt et gestion professionnelle de votre portefeuille.',
                'icon' => 'fas fa-piggy-bank',
                'features' => [
                    'Déduction fiscale immédiate',
                    'Croissance à l\'abri de l\'impôt',
                    'Report d\'impôt',
                    'RAP et REEP disponibles',
                    'Gestion professionnelle',
                    'Diversification des investissements',
                    'Conseils en planification'
                ],
                'base_price' => 1.50,
                'pricing_rules' => [
                    'management_fee_percentage' => 1.5,
                    'minimum_contribution' => 500
                ],
                'is_active' => true,
                'sort_order' => 3,
                'meta_title' => 'REER Montréal - Épargne Retraite iA Groupe financier',
                'meta_description' => 'REER avec avantages fiscaux immédiats. Gestion professionnelle, diversification, RAP et REEP. Planifiez votre retraite avec nos experts.'
            ],
            [
                'name' => 'CELI',
                'slug' => 'celi',
                'description' => 'Compte d\'épargne libre d\'impôt pour tous vos projets',
                'detailed_description' => 'Le CELI vous permet d\'épargner et d\'investir en franchise d\'impôt. Retraits libres d\'impôt, aucun âge limite et récupération des droits de cotisation font du CELI un outil d\'épargne très flexible.',
                'icon' => 'fas fa-coins',
                'features' => [
                    'Retraits libres d\'impôt',
                    'Croissance libre d\'impôt',
                    'Aucun âge limite',
                    'Droits récupérables',
                    'Gestion professionnelle',
                    'Diversification des investissements',
                    'Flexibilité maximale'
                ],
                'base_price' => 1.20,
                'pricing_rules' => [
                    'management_fee_percentage' => 1.2,
                    'minimum_contribution' => 500
                ],
                'is_active' => true,
                'sort_order' => 4,
                'meta_title' => 'CELI Montréal - Compte Épargne Libre Impôt iA',
                'meta_description' => 'CELI avec croissance libre d\'impôt. Retraits flexibles, aucun âge limite. Investissez pour tous vos projets avec nos experts.'
            ]
        ];

        foreach ($services as $service) {
            Service::updateOrCreate(
                ['slug' => $service['slug']],
                $service
            );
        }
    }
}
