<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class DefaultSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Informations générales
            [
                'key' => 'site_name',
                'value' => 'Assurance iA Montréal',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Nom du site',
                'description' => 'Nom affiché dans le header et les métadonnées'
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Votre protection, notre priorité',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Slogan du site',
                'description' => 'Slogan affiché sous le nom du site'
            ],
            [
                'key' => 'site_description',
                'value' => 'Assurance iA Montréal, votre partenaire de confiance pour tous vos besoins d\'assurance. Visa visiteur, assurance vie, REER, CELI.',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'Description du site',
                'description' => 'Description utilisée pour le SEO'
            ],

            // Coordonnées
            [
                'key' => 'contact_phone',
                'value' => '(*************',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Téléphone',
                'description' => 'Numéro de téléphone principal'
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Email',
                'description' => 'Adresse email principale'
            ],
            [
                'key' => 'contact_address',
                'value' => 'Montréal, Québec, Canada',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Adresse',
                'description' => 'Adresse physique'
            ],
            [
                'key' => 'business_hours',
                'value' => 'Lundi - Vendredi: 9h00 - 17h00',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Heures d\'ouverture',
                'description' => 'Heures d\'ouverture du bureau'
            ],

            // Paramètres de simulation
            [
                'key' => 'visa_base_rate',
                'value' => '2.5',
                'type' => 'number',
                'group' => 'simulation',
                'label' => 'Taux de base visa visiteur',
                'description' => 'Taux de base par jour par 1000$ de couverture'
            ],
            [
                'key' => 'life_base_rate',
                'value' => '0.8',
                'type' => 'number',
                'group' => 'simulation',
                'label' => 'Taux de base assurance vie',
                'description' => 'Taux de base par mois par 1000$ de couverture'
            ],
            [
                'key' => 'reer_management_fee',
                'value' => '1.5',
                'type' => 'number',
                'group' => 'simulation',
                'label' => 'Frais de gestion REER (%)',
                'description' => 'Pourcentage de frais de gestion annuel pour REER'
            ],
            [
                'key' => 'celi_management_fee',
                'value' => '1.2',
                'type' => 'number',
                'group' => 'simulation',
                'label' => 'Frais de gestion CELI (%)',
                'description' => 'Pourcentage de frais de gestion annuel pour CELI'
            ],

            // Réseaux sociaux
            [
                'key' => 'facebook_url',
                'value' => '',
                'type' => 'text',
                'group' => 'social',
                'label' => 'URL Facebook',
                'description' => 'Lien vers la page Facebook'
            ],
            [
                'key' => 'linkedin_url',
                'value' => '',
                'type' => 'text',
                'group' => 'social',
                'label' => 'URL LinkedIn',
                'description' => 'Lien vers la page LinkedIn'
            ],

            // SEO
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Google Analytics ID',
                'description' => 'ID de suivi Google Analytics'
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'assurance, visa visiteur, assurance vie, REER, CELI, Montréal, iA',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Mots-clés méta',
                'description' => 'Mots-clés pour le référencement'
            ]
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
