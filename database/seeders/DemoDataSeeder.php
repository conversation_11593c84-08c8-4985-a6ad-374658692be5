<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SimulationLead;
use App\Models\Contract;
use App\Models\Appointment;
use Carbon\Carbon;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur admin
        $admin = User::create([
            'name' => 'Admin iA Montréal',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => bcrypt('password123'),
        ]);

        // Créer quelques leads de simulation
        $leads = [
            [
                'first_name' => 'Marie',
                'last_name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'insurance_type' => 'visa_visiteur',
                'simulation_data' => [
                    'insurance_type' => 'visa_visiteur',
                    'age' => 35,
                    'coverage_amount' => 50000,
                    'duration' => 30,
                    'country' => 'france'
                ],
                'estimated_amount' => 125.50,
                'status' => 'new'
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'insurance_type' => 'assurance_vie',
                'simulation_data' => [
                    'insurance_type' => 'assurance_vie',
                    'age' => 42,
                    'coverage_amount' => 250000,
                    'duration' => 20
                ],
                'estimated_amount' => 480.00,
                'status' => 'contacted'
            ],
            [
                'first_name' => 'Sophie',
                'last_name' => 'Tremblay',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'insurance_type' => 'reer',
                'simulation_data' => [
                    'insurance_type' => 'reer',
                    'age' => 28,
                    'coverage_amount' => 15000,
                    'duration' => 25
                ],
                'estimated_amount' => 225.00,
                'status' => 'new'
            ]
        ];

        foreach ($leads as $leadData) {
            SimulationLead::create($leadData);
        }

        // Créer quelques contrats
        $contracts = [
            [
                'client_name' => 'Pierre Lavoie',
                'client_email' => '<EMAIL>',
                'client_phone' => '(*************',
                'contract_number' => Contract::generateContractNumber(),
                'type' => 'visa_visiteur',
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->addDays(7), // Expire dans 7 jours
                'amount' => 150.00,
                'status' => 'active',
                'contract_details' => [
                    'coverage_amount' => 75000,
                    'country' => 'belgique',
                    'duration' => 45
                ]
            ],
            [
                'client_name' => 'Isabelle Roy',
                'client_email' => '<EMAIL>',
                'client_phone' => '(*************',
                'contract_number' => Contract::generateContractNumber(),
                'type' => 'assurance_vie',
                'start_date' => Carbon::now()->subYear(),
                'end_date' => Carbon::now()->addMonths(11),
                'amount' => 520.00,
                'status' => 'active',
                'contract_details' => [
                    'coverage_amount' => 300000,
                    'type' => 'vie_temporaire'
                ]
            ]
        ];

        foreach ($contracts as $contractData) {
            Contract::create($contractData);
        }

        // Créer quelques rendez-vous
        $appointments = [
            [
                'client_name' => 'Michel Gagnon',
                'client_email' => '<EMAIL>',
                'client_phone' => '(*************',
                'service_type' => 'consultation_generale',
                'appointment_date' => Carbon::now()->addDays(2)->setTime(10, 0),
                'status' => 'confirmed',
                'message' => 'Intéressé par une assurance vie pour ma famille'
            ],
            [
                'client_name' => 'Nathalie Côté',
                'client_email' => '<EMAIL>',
                'client_phone' => '(*************',
                'service_type' => 'visa_visiteur',
                'appointment_date' => Carbon::now()->addDays(5)->setTime(14, 30),
                'status' => 'pending',
                'message' => 'Besoin d\'assurance pour voyage en Europe'
            ]
        ];

        foreach ($appointments as $appointmentData) {
            Appointment::create($appointmentData);
        }

        $this->command->info('Données de démonstration créées avec succès !');
        $this->command->info('Admin: <EMAIL> / password123');
    }
}
