<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AppointmentController;

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Routes pour les rendez-vous
// API dynamique pour générer les créneaux selon le mois/année demandé
Route::get('/appointments/available-slots', function(\Illuminate\Http\Request $request) {
    $year = $request->get('year', \Carbon\Carbon::now()->year);
    $month = $request->get('month', \Carbon\Carbon::now()->month);

    // Validation des paramètres
    if ($year < \Carbon\Carbon::now()->year ||
        ($year == \Carbon\Carbon::now()->year && $month < \Carbon\Carbon::now()->month)) {
        return response()->json([]);
    }

    $slots = [];
    $startDate = \Carbon\Carbon::create($year, $month, 1);
    $endDate = $startDate->copy()->endOfMonth();

    // Si c'est le mois actuel, commencer à partir de demain
    if ($startDate->format('Y-m') === \Carbon\Carbon::now()->format('Y-m')) {
        $startDate = \Carbon\Carbon::now()->addDay();
    }

    $workingHours = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'];

    // Récupérer les rendez-vous existants pour ce mois
    $bookedSlots = \App\Models\Appointment::whereBetween('appointment_date', [$startDate, $endDate])
        ->whereIn('status', ['pending', 'confirmed'])
        ->pluck('appointment_date')
        ->map(function($date) {
            return \Carbon\Carbon::parse($date)->format('Y-m-d H:i:s');
        })
        ->toArray();

    $currentDate = $startDate->copy();
    while ($currentDate <= $endDate) {
        if ($currentDate->isWeekday()) {
            $dateKey = $currentDate->format('Y-m-d');
            $daySlots = [];

            foreach ($workingHours as $hour) {
                $slotDateTime = $currentDate->copy()->setTimeFromTimeString($hour);
                $isBooked = in_array($slotDateTime->format('Y-m-d H:i:s'), $bookedSlots);

                if (!$isBooked) {
                    $daySlots[] = [
                        'time' => $hour,
                        'datetime' => $slotDateTime->format('Y-m-d H:i:s'),
                        'formatted' => $slotDateTime->format('H:i')
                    ];
                }
            }

            // Ajouter le jour seulement s'il y a des créneaux disponibles
            if (!empty($daySlots)) {
                $slots[] = [
                    'date' => $currentDate->format('Y-m-d'),
                    'date_formatted' => $currentDate->locale('fr')->isoFormat('dddd D MMMM YYYY'),
                    'times' => $daySlots
                ];
            }
        }
        $currentDate->addDay();
    }

    return response()->json($slots);
});

// Route originale commentée temporairement
// Route::get('/appointments/available-slots', [AppointmentController::class, 'getAvailableSlots']);

// Route de test pour déboguer
Route::get('/test-dates', function() {
    $startDate = \Carbon\Carbon::now()->addDay();
    $endDate = \Carbon\Carbon::now()->addDays(90);
    return response()->json([
        'start' => $startDate->format('Y-m-d'),
        'end' => $endDate->format('Y-m-d'),
        'days_diff' => $startDate->diffInDays($endDate)
    ]);
});

// Route de test pour les créneaux
Route::get('/test-slots', function() {
    $slots = [];
    $startDate = \Carbon\Carbon::now()->addDay();
    $endDate = \Carbon\Carbon::now()->addDays(90);

    $workingHours = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30'];

    $currentDate = $startDate->copy();
    while ($currentDate <= $endDate) {
        if ($currentDate->isWeekday()) {
            $dateKey = $currentDate->format('Y-m-d');
            $slots[$dateKey] = [
                'date' => $currentDate->format('Y-m-d'),
                'date_formatted' => $currentDate->locale('fr')->isoFormat('dddd D MMMM YYYY'),
                'times' => []
            ];
            foreach ($workingHours as $hour) {
                $slotDateTime = $currentDate->copy()->setTimeFromTimeString($hour);
                $slots[$dateKey]['times'][] = [
                    'time' => $hour,
                    'datetime' => $slotDateTime->format('Y-m-d H:i:s'),
                    'formatted' => $slotDateTime->format('H:i')
                ];
            }
        }
        $currentDate->addDay();
    }

    return response()->json(array_values($slots));
});
