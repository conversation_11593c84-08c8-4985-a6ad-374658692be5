<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Tâches planifiées
Schedule::command('contracts:send-reminders --days=7')
    ->dailyAt('09:00')
    ->description('Envoyer les rappels de contrats expirant dans 7 jours');

Schedule::command('contracts:send-reminders --days=1')
    ->dailyAt('10:00')
    ->description('Envoyer les rappels de contrats expirant demain');

// Nettoyage des anciennes données
Schedule::command('model:prune')
    ->daily()
    ->description('Nettoyer les anciennes données');

// Backup de la base de données (optionnel)
Schedule::command('backup:database')
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->description('Sauvegarde hebdomadaire de la base de données');
