<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\SimulationController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\Admin\AdminController;
use Illuminate\Support\Facades\Route;

// Routes publiques
Route::get('/', [HomeController::class, 'index'])->name('home');

// Route de test pour vérifier les services
Route::get('/test-services', function () {
    $services = \App\Models\Service::active()->ordered()->get();
    return response()->json([
        'count' => $services->count(),
        'services' => $services->map(function($service) {
            return [
                'name' => $service->name,
                'description' => $service->description,
                'icon' => $service->icon,
                'features' => $service->features,
                'is_active' => $service->is_active
            ];
        })
    ]);
});
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'submitContact'])->name('contact.submit');
Route::get('/legal', [HomeController::class, 'legal'])->name('legal');

// Routes de simulation
Route::get('/simulation', [SimulationController::class, 'index'])->name('simulation.index');
Route::post('/simulation', [SimulationController::class, 'calculate'])->name('simulation.calculate');
Route::get('/lead-info', [SimulationController::class, 'leadInfo'])->name('simulation.lead-info');
Route::post('/lead-info', [SimulationController::class, 'storeLead'])->name('simulation.store-lead');
Route::get('/simulation-result', [SimulationController::class, 'result'])->name('simulation.result');
Route::get('/simulation/pdf/{id}', [SimulationController::class, 'generatePdf'])->name('simulation.pdf');

// Routes de rendez-vous
Route::get('/appointment', [AppointmentController::class, 'create'])->name('appointment.create');
Route::post('/appointment', [AppointmentController::class, 'store'])->name('appointment.store');
Route::get('/appointment/success', [AppointmentController::class, 'success'])->name('appointment.success');

// Dashboard utilisateur authentifié - Redirection vers admin
Route::get('/dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Routes admin protégées
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/leads', [AdminController::class, 'leads'])->name('leads');
    Route::get('/contracts', [AdminController::class, 'contracts'])->name('contracts');
    Route::get('/appointments', [AdminController::class, 'appointments'])->name('appointments');
    Route::patch('/leads/{lead}', [AdminController::class, 'updateLead'])->name('leads.update');
    Route::delete('/leads/{lead}', [AdminController::class, 'deleteLead'])->name('leads.delete');
    Route::get('/leads-diagnostic', [AdminController::class, 'leadsDiagnostic'])->name('leads.diagnostic');
    Route::patch('/appointments/{appointment}', [AdminController::class, 'updateAppointment'])->name('appointments.update');

    // Gestion des services
    Route::resource('services', \App\Http\Controllers\Admin\ServiceController::class);

    // Gestion des paramètres
    Route::get('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
    Route::put('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');
    Route::post('/settings', [\App\Http\Controllers\Admin\SettingController::class, 'store'])->name('settings.store');
    Route::delete('/settings/{setting}', [\App\Http\Controllers\Admin\SettingController::class, 'destroy'])->name('settings.destroy');

    // Gestion du contenu
    Route::resource('content', \App\Http\Controllers\Admin\ContentController::class);

    // Gestion du contenu de la page d'accueil
    Route::get('/home-content', [\App\Http\Controllers\Admin\HomeContentController::class, 'edit'])->name('home-content.edit');
    Route::put('/home-content', [\App\Http\Controllers\Admin\HomeContentController::class, 'update'])->name('home-content.update');
});



require __DIR__.'/auth.php';
