<?php

require_once 'vendor/autoload.php';

use App\Models\Service;

// Charger l'application Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Tester les services
echo "=== Test des services ===\n";

$services = Service::active()->ordered()->get();

echo "Nombre de services actifs : " . $services->count() . "\n\n";

foreach ($services as $service) {
    echo "Nom : " . $service->name . "\n";
    echo "Description : " . $service->description . "\n";
    echo "Icône : " . $service->icon . "\n";
    echo "Features : " . json_encode($service->features, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    echo "Actif : " . ($service->is_active ? 'Oui' : 'Non') . "\n";
    echo "---\n";
}
