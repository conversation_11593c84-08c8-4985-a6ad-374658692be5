<?php

$url = 'http://localhost/aaa/public/';
$content = file_get_contents($url);

if ($content === false) {
    echo "❌ Impossible de récupérer le contenu de la page\n";
    exit(1);
}

echo "=== Test du contenu de la page d'accueil ===\n";
echo "Taille du contenu : " . strlen($content) . " caractères\n\n";

// Rechercher les services dans le contenu
$services_to_check = [
    'Protection médicale complète pour vos voyages au Canada',
    'Protégez votre famille avec notre assurance vie',
    'Régime enregistré d\'épargne-retraite pour optimiser',
    'Compte d\'épargne libre d\'impôt pour tous vos projets'
];

$found_services = 0;
foreach ($services_to_check as $service_text) {
    if (strpos($content, $service_text) !== false) {
        echo "✅ Trouvé : " . substr($service_text, 0, 50) . "...\n";
        $found_services++;
    } else {
        echo "❌ Non trouvé : " . substr($service_text, 0, 50) . "...\n";
    }
}

echo "\nRésultat : $found_services/4 services trouvés\n";

// Vérifier si les anciennes données codées en dur sont encore présentes
$old_texts = [
    'Protection complète pour vos voyages au Canada',
    'Sécurisez l\'avenir de votre famille'
];

$old_found = 0;
foreach ($old_texts as $old_text) {
    if (strpos($content, $old_text) !== false) {
        echo "⚠️  Ancien texte encore présent : " . substr($old_text, 0, 50) . "...\n";
        $old_found++;
    }
}

if ($old_found === 0) {
    echo "✅ Aucun ancien texte codé en dur trouvé\n";
}

// Sauvegarder un extrait pour inspection
file_put_contents('page_extract.html', substr($content, 0, 5000));
echo "\nExtrait sauvegardé dans page_extract.html\n";
